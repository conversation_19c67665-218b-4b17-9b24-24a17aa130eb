<!-- 值班值守人员 - Page -->
<template>
	<div class="shift-change-container" :style="{height: heightTable+'px'}">
		<div style="width: 45%;">
			<h1 class="title">今日交班：</h1>

			<div class="shift-section">
				<div v-for="(item, index) in yesTodayData" :key="index" class="shift-group">
        <div class="shift-row">
          <span class="label">{{ formatpositionNamee(item.positionName) }}：</span>
          <span class="value" >{{ item.nameList[0] }}</span>
        </div>
        
        <div v-if="item.nameList.length > 1">
          <div v-for="(name, nameIndex) in item.nameList.slice(1)" 
               :key="nameIndex" 
               class="shift-row indent">
            <span class="value">{{ name }}</span>
          </div>
        </div>
      </div>
			</div>
		</div>

		<div class="arrow-container">
			<!-- <div class="arrow"></div> -->
			<img src="@/assets/images/dutyShiftTabRight.png" alt="向右箭头" class="arrow-img">
		</div>


		<div style="width: 45%;">
			<h1 class="title">今日接班：</h1>

			<div class="shift-section">
				<div v-for="(item, index) in todayData" :key="index" class="shift-group">
        <div class="shift-row">
          <span class="label">{{ formatpositionNamee(item.positionName) }}：</span>
          <span class="value" >{{ item.nameList[0] }}</span>
        </div>
        
        <div v-if="item.nameList.length > 1">
          <div v-for="(name, nameIndex) in item.nameList.slice(1)" 
               :key="nameIndex" 
               class="shift-row indent">
            <span class="value">{{ name }}</span>
          </div>
        </div>
      </div>
			</div>
		</div>
  </div>
</template>

<script>

import { dutyManagementApi } from "@/api";


export default {
  name: "dutyShiftTab",
  data() {
    return {
			heightTable: 700,
			yesTodayData: [
				{
					positionName: "带班领导",
					nameList: [
						"闫   伟、徐   毅",
					]
				},
				{
					positionName: "带班处长",
					nameList: [
						"于云飞（主）、张   聪（副）",
					]
				},
				{
					positionName: "赴现场处长",
					nameList: [
						"王新华（主）、石   毅（副）",
					]
				},
				{
					positionName: "指挥调度单元",
					nameList: [
						"贾桂波（综合管理岗）",
						"吴梦旭（指挥调度岗）",
						"李   政、李   昊（信息编辑岗）",
						"周英男、胡义祥（协调联络岗）"
					]
				},
				{
					positionName: "综合保障单元",
					nameList: [
						"陈   旭、刘子杰、淦   悦、王   志、李   彤（信息化保障岗）",
						"廖   丹（救援协调岗）",
						"周   媛（高危监测岗）",
						"丁宇轩、李   进（预警发布岗）"
					]
				},
				{
					positionName: "防汛值班",
					nameList: [
						"付   侃、梁   轩"
					]
				},
			],
			todayData: [
				{
					positionName: "带班领导",
					nameList: [
						"靳玉光、陈连勇",
					]
				},
				{
					positionName: "带班处长",
					nameList: [
						"黄   亮（主）、庄    瑞（副）",
					]
				},
				{
					positionName: "赴现场处长",
					nameList: [
						"姜有龙（主）、何明明（副",
					]
				},
				{
					positionName: "指挥调度单元",
					nameList: [
						"肖   杰（综合管理岗）",
						"张   云（指挥调度岗）",
						"张博林、李赫男（信息编辑岗）",
						"曹宝珠、周奕辰（协调联络岗）"
					]
				},
				{
					positionName: "综合保障单元",
					nameList: [
						"郭咏梅、王   宇、李   彤、吴东东、李   让（信息化保障岗）",
						"王亚峰（救援协调岗）",
						"周   媛（高危监测岗）",
						"王   莹、林   倩（预警发布岗）"
					]
				},
				{
					positionName: "防汛值班",
					nameList: [
						"刘博超、赵荣华"
					]
				},
			],
    };
  },
	mounted() {
		const gaping = 50 + 40 + 12 + 20 + 45 + 50;
		this.heightTable = window.innerHeight - gaping;
		this.initDutyData();
	},
  methods: {
		formatpositionNamee(name) {
      // 在中文字符之间添加空格
      return name.split('').join(' ').replace(/\s+/g, ' ').trim();
    },
		initDutyData() {
			dutyManagementApi.queryShiftingDuty().then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.yesTodayData = data?.yesToday;
        this.todayData = data?.today;
      });
		}
  }
};
</script>


<style lang="scss" scoped>
.shift-change-container {
  display: flex;
  font-family: "Microsoft YaHei", SimHei, sans-serif;
  padding: 20px;
  // max-width: 800px;
  width: 100%;
	// height: 100%;
  margin: 0 auto;
  color: #333;
	border: 1px solid var(--themeColor);
	border-radius: 5px;
}

.arrow-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10%; 
  padding: 0 20px;
}

// .arrow-img {
//   width: 60px; 
//   height: auto;
// }

// .arrow {
//   width: 0; 
//   height: 0; 
//   border-top: 30px solid transparent;
//   border-bottom: 30px solid transparent;
//   border-left: 50px solid #666; 
// }

.title {
  font-size: 18px;
  font-weight: bold;
  margin: 20px 0 10px 0;
  text-align: center;
}

.shift-section {
  margin-bottom: 30px;
  border: 1px solid var(--themeColor);
  padding: 15px;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.shift-group {
  margin-bottom: 20px;
}

.shift-row {
  margin: 18px 0;
  display: flex;
}

.indent {
	// 与label宽度一致实现缩进效果
  padding-left: 150px; 
}

.label {
  width: 150px;
  font-weight: bold;
  text-align: right;
  padding-right: 10px;
  flex-shrink: 0;
}

.value {
  flex-grow: 1;
}
</style>