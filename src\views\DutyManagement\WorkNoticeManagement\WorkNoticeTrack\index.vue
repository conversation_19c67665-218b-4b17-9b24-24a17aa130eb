<!-- 工作通知跟踪---WorkNoticeTrack -->
 <template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      :showConfirm="false"
    >
      <workDetail ref="workDetailRef"></workDetail>
    </general-dialog>

    <!-- <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, dutyManagementApi, orgApi } from "@/api";
import { getItemList, workNoticeType } from "@/utils/dictionary";

import { conversionDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 
import workDetail from "./components/workDetail.vue";

export default {
  name: "WorkNoticeTrack",
  components: {
    GeneralDialog,
    PortalTable,
    workDetail,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "title",
          label: "通知标题",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          label: "类型",
          prop: "noticeType",
          type: "select",
          placeholder: "请选择",
          width: "150",
          options: [],
        },
        {
          label: "接收状况",
          prop: "recvStatus",
          type: "select",
          placeholder: "请选择",
          width: "150",
          options: [
            {label: "未接收", value: "0"},
            {label: "已经全部接收", value: "1"},
            {label: "部分未接收", value: "2"},
          ],
        },
      ],
      columns: [
        { prop: "title", label: "通知标题", text: true },
        { prop: "noticeType", label: "类型", text: true },
        { prop: "publishTime", label: "发布时间", text: true },
        { prop: "recvStatus", label: "接收状况", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '160px',
          operationList: [
            {
              label: '查看',
              permission: 'noticeTrack:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '重发',
              permission: 'noticeTrack:resend',
              buttonClick: this.handleResend,
              isShow: (row, $index) => {
                if(this.tableData[$index].recvStatusNum === '1'){
                    return true
                }else{
                    return false
                }
              }
            },
            {
              label: '提醒',
              permission: 'noticeTrack:remind',
              buttonClick: this.handleRemind,
              isShow: (row, $index) => {
                if(this.tableData[$index].recvStatusNum != "1"){
                    return true
                }else{
                    return false
                }
              }
            },
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      noticeTypeList: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "960px",
      generalDialogTitle: "新增工作通知内容",

      // 单位列表
      orgTree:[],
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      selectOrgData: [],
      // feedbackList:[],
    };
  },
  mounted() {
    this.getTableDataList();
    this.queryDictionaryType();
    // this.registerHandlers();
  },
  methods: {
    // registerHandlers() {
    //   this.$store.commit("generalEvent/registerEventHandler", {
    //     type: "add_top",
    //     handler: this.handleAdd,
    //   });
    // },
    // handleChangeMonth(value) {
    //   this.form.inspectionTime = conversionDate(value);
    // },
    
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.noticeTypeList = await getItemList(workNoticeType);
        this.searchItems[1].options = this.noticeTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      // this.fileList = [];
      // this.form.fileList = [];
      dutyManagementApi.queryNotifyTrackInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$refs.workDetailRef.showDetailData(data);
        this.generalDialogTitle = data?.title;

        // if (data.fileList && data.fileList.length > 0) {
        //   data.fileList.forEach((row) => {
        //     this.fileList.push({
        //       name: row.fileName,
        //       url: this.fileBaseUrl + row.fileUrl,
        //       id: row.id,
        //     });
        //   });
        // } else {
        //   data.fileList = [];
        // }
        // this.form = {
        //   ...data,
        // };
      });
    },

    //新增
    // handleAdd() {
    //   this.queryOrgTreeDataList();
    //   this.styleType = 1;
    //   this.dialogVisible = true;
    //   // this.form = {};
    //   this.resetFormData();
    //   this.generalDialogTitle = "新增工作通知内容";
    // },

    // //编辑
    // handleEdit(row) {
    //   this.queryOrgTreeDataList();
    //   this.styleType = 2;
    //   this.dialogVisible = true;
    //   this.getRowDataInfo(row);
    //   this.generalDialogTitle = "编辑工作通知内容";
    // },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
    },

    // 重发
    handleResend(row) {
      this.$confirm('确定重发此条通知, 是否继续?', '重发', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dutyManagementApi.workNoticeSendAgain({ id: row.id }).then((res) => {
          const { code, data, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.getTableDataList();
          this.$message.success("重发成功！");
        });
      })
      // setTimeout(() => {
      //   this.$message.success("重发成功！");
      // }, 500);
    },

    // 提醒
    handleRemind() {
      setTimeout(() => {
        this.$message.success("提醒成功！");
      }, 500);
    },

    //删除
    // handleDelete(row) {
    //   this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
    //     confirmButtonText: '确定',
    //     cancelButtonText: '取消',
    //     type: 'warning'
    //   }).then(() => {
    //     dutyManagementApi.deleteContentManage({ id: row.id }).then((res) => {
    //       const { code, message, error } = res;
    //       if (code !== 0) return this.$message.error(message || error);
    //       this.$message.success('删除成功');
    //       this.handSubmitSuccess();
    //     })
    //   })
    // },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryNotifyTrackPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },
    
    // 提交表单
    // handleSubmit() {
    //   this.$refs.addForm.validate(async (valid) => {
    //     if (valid) {
    //       if (this.styleType === 1) {
    //         const res = await dutyManagementApi.createContentManage(this.form);
    //         const {code, error} = res;
    //         if (code === 0) {
    //           this.$message.success('新增成功')
    //           this.handSubmitSuccess();
    //         } else {
    //           this.$message.error(error)
    //         }
    //       } else {
    //         const res = await dutyManagementApi.updateContentManage(this.form);
    //         const {code, error} = res;
    //         if (code === 0) {
    //           this.$message.success('修改成功')
    //           this.handSubmitSuccess();
    //         } else {
    //           this.$message.error(error)
    //         }
    //       }
    //     } else {
    //       return false;
    //     }
    //   });
    // },

    // handSubmitSuccess() {
    //   this.getTableDataList();
    //   this.dialogVisible = false;
    //   // this.$refs.addForm.resetFields();
    //   this.resetFormData();
    // },

    resetFormData() {
      this.form = {
        noticeType: "",
        title: "",
        receiveList: [],
        content: "",
        feedback: "",
        feedbackList: "",
        feedbackData: "",
        publishTime: "",
        pushChannelList: [],
        fileList:[]
      };
      this.selectOrgData = [];
      this.fileList = [];
      this.form.fileList = [];
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
