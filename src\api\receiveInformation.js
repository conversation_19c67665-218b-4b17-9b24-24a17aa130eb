import request from "@/utils/request";

export default {
  // 查询接报信息列表
  queryReportInfoList(params) {
    return request({
      url: "/ds/infoAggregation/queryReportInfoList",
      method: "post",
      data: params,
    });
  },

  // 查询事件处理信息列表
  queryEventProcessInfoList(params) {
    return request({
      url: "/ds/infoAggregation/queryEventProcessInfoList",
      method: "post",
      data: params,
    });
  },

  // 查询事件过程详情
  queryEventProcessInfo(params) {
    return request({
      url: "/ds/infoAggregation/queryEventProcessInfo",
      method: "post",
      data: params,
    });
  },

  // 批量查询事件处理信息详情
  batchQueryEventProcessDetails(params) {
    return request({
      url: "/ds/infoAggregation/batchQueryProcessDetails",
      method: "post",
      data: params,
    });
  },

  // 创建接报信息
  createReportInfo(params) {
    return request({
      url: "/ds/infoAggregation/createReportInfo",
      method: "post",
      data: params,
    });
  },

  // 创建续报信息
  createEventProcessInfo(params) {
    return request({
      url: "/ds/infoAggregation/createEventProcessInfo",
      method: "post",
      data: params,
    });
  },

  // 查询北京区县街道
  queryTownsBeijing(params) {
    return request({
      url: "/ds/townsBeijing/query",
      method: "post",
      data: params,
    });
  },

  // 导出接报信息列表
  exportList(params) {
    return request({
      url: "/ds/infoAggregation/exportList",
      method: "post",
      data: params,
      responseType: "blob",
    });
  },

  // 获取接报信息详情
  getReportInfoDetail(id) {
    return request({
      url: `/ds/infoAggregation/getReportInfoDetail/${id}`,
      method: "get",
    });
  },

  // 查询接报信息详情（新接口）
  queryReportInfo(params) {
    return request({
      url: "/ds/infoAggregation/queryReportInfo",
      method: "post",
      data: params,
    });
  },
  // 续报
  updateReportInfo(params) {
    return request({
      url: "/ds/infoAggregation/updateReportInfo",
      method: "post",
      data: params,
    });
  },
  // 查询任务反馈分页列表
  queryResponseTaskBackPage(params) {
    return request({
      url: "/ds/taskBack/queryResponseTaskBackPage",
      method: "post",
      data: params,
    });
  },
};
