import request from "@/utils/request";

/**
 * 指令配置相关API接口
 */
export default {
  /**
   * 查询指令配置分页列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码 (从1开始)
   * @param {number} params.count - 每页数量
   * @param {string} params.accidentType - 事故类型
   * @param {string} params.accidentSubType - 事故小类
   * @param {string} params.orgId - 机构ID
   * @param {string} params.commandCenter - 指挥部
   * @param {string} params.role - 角色
   * @returns {Promise} API响应
   */
  queryResponseCommandPage(params) {
    return request({
      url: "/ds/command/queryResponseCommandPage",
      method: "post",
      data: params,
    });
  },

  /**
   * 新增指令配置
   * @param {Object} params - 指令配置数据
   * @returns {Promise} API响应
   */
  createResponseCommand(params) {
    return request({
      url: "/ds/command/createResponseCommand",
      method: "post",
      data: params,
    });
  },

  /**
   * 更新指令配置
   * @param {Object} params - 指令配置数据
   * @returns {Promise} API响应
   */
  updateResponseCommand(params) {
    return request({
      url: "/ds/command/updateResponseCommand",
      method: "post",
      data: params,
    });
  },

  /**
   * 删除指令配置
   * @param {Object} params - 删除参数
   * @param {string} params.id - 指令配置ID
   * @returns {Promise} API响应
   */
  deleteResponseCommand(params) {
    return request({
      url: "/ds/command/deleteResponseCommand",
      method: "post",
      data: params,
    });
  },

  /**
   * 查询指令配置详情
   * @param {Object} params - 查询参数
   * @param {string} params.id - 指令配置ID
   * @returns {Promise} API响应
   */
  queryResponseCommandInfo(params) {
    return request({
      url: "/ds/command/queryResponseCommandInfo",
      method: "post",
      data: params,
    });
  },

  /**
   * 任务同步
   * @param {Object} params - 同步参数
   * @returns {Promise} API响应
   */
  syncTasks(params) {
    return request({
      url: "/ds/command/syncTasks",
      method: "post",
      data: params,
    });
  },

  /**
   * 任务下发
   * @param {Object} params - 下发参数
   * @param {string} params.infoIds - 事件集合，点击列表复选框的ids
   * @returns {Promise} API响应
   */
  assignTasks(params) {
    return request({
      url: "/ds/command/assignTasks",
      method: "post",
      data: params,
    });
  },
};
