// 接报信息相关工具函数和常量

// 表单验证规则
export const formRules = {
  infoTitle: [{ required: true, message: "事件标题不能为空", trigger: "blur" }],
  infoTime: [
    { required: true, message: "事发时间不能为空", trigger: "change" },
  ],
  locationPath: [
    { required: true, message: "请选择事件地点", trigger: "change" },
  ],
  infoChildType: [
    { required: true, message: "请选择事件类型", trigger: "change" },
  ],
  eventDetail: [
    { required: true, message: "事件详情不能为空", trigger: "blur" },
  ],
  reportMethod: [
    { required: true, message: "请选择接报方式", trigger: "change" },
  ],
};

// 初始表单数据
export const getInitialFormData = () => ({
  infoTitle: "",
  infoTime: "",
  locationPath: [],
  longitude: "",
  latitude: "",
  locationDetail: "",
  infoChildType: "",
  deathCount: 0,
  missingCount: 0,
  seriousInjuryCount: 0,
  minorInjuryCount: 0,
  eventDetail: "",
  reportMethod: "",
  reportUnit: "",
  reportPerson: "",
  attentionStatus: 0,
  infoStatus: 0,
  status: 0,
});

// 地点级联选择器配置
export const locationCascaderProps = {
  value: "id",
  label: "name",
  children: "children",
  checkStrictly: false,
};

// 状态映射
export const statusMap = {
  // 数字状态映射
  0: "待办",
  1: "已办",
  2: "归档",
  // 中文状态映射（兼容接口返回）
  待办: "待办",
  已办: "已办",
  归档: "归档",
};

// 事件状态映射
export const infoStatusMap = {
  // 数字状态映射
  0: "正在处置",
  1: "启动响应",
  2: "处置完毕",
  // 中文状态映射（兼容接口返回）
  正在处置: "正在处置",
  启动响应: "启动响应",
  处置完毕: "处置完毕",
};

// 数据转换函数
export const transformLocationData = (data) => {
  if (!Array.isArray(data)) return [];

  return data.map((district) => {
    const getFieldValue = (obj, fields) =>
      fields.find((field) => obj[field])
        ? obj[fields.find((field) => obj[field])]
        : "";

    const districtItem = {
      id: getFieldValue(district, ["code", "id", "value"]),
      name: getFieldValue(district, [
        "townName",
        "name",
        "label",
        "districtName",
      ]),
      children: [],
    };

    const childrenFields = ["child", "children", "streets"];
    const childrenData = childrenFields.find(
      (field) => district[field] && Array.isArray(district[field])
    );

    if (childrenData) {
      districtItem.children = district[childrenData].map((street) => ({
        id: getFieldValue(street, ["code", "id", "value"]),
        name: getFieldValue(street, [
          "townName",
          "name",
          "label",
          "streetName",
        ]),
      }));
    }

    return districtItem;
  });
};

// 格式化提交数据
export const formatSubmitData = (form) => {
  const data = {
    // 基本信息
    infoTitle: form.infoTitle,
    infoTime: form.infoTime || "",
    infoType: form.infoType || form.infoChildType,
    infoChildType: form.infoChildType,

    // 地理位置信息
    infoDistrict:
      form.locationPath && form.locationPath.length > 0
        ? form.locationPath[0]
        : "",
    infoTownshipStreet:
      form.locationPath && form.locationPath.length > 1
        ? form.locationPath[1]
        : "",
    infoLongitude: form.longitude || "",
    infoLatitude: form.latitude || "",
    infoLocationDetail: form.locationDetail || "",

    // 伤亡统计
    deathNum: parseInt(form.deathCount) || 0,
    missingNum: parseInt(form.missingCount) || 0,
    severeInjuryNum: parseInt(form.seriousInjuryCount) || 0,
    lightInjuryNum: parseInt(form.minorInjuryCount) || 0,

    // 事件详情
    eventInfo: form.eventDetail || "",

    // 报送信息
    infoReportingMethod: form.reportMethod || "",
    infoReportingUnit: form.reportUnit || "",
    infoReportingUser: form.reportPerson || "",

    // 状态字段
    attentionStatus: parseInt(form.attentionStatus) || 0,
    infoStatus: parseInt(form.infoStatus) || 0,
    status: parseInt(form.status) || 0,
    isDel: 0,
  };

  // 时间格式化处理
  if (data.infoTime) {
    const date = new Date(data.infoTime);
    if (!isNaN(date.getTime())) {
      data.infoTime = date.toISOString().slice(0, 19).replace("T", " ");
    }
  }

  return data;
};

// 填充表单数据（编辑模式）
export const fillFormWithRowData = (row) => {
  return {
    infoTitle: row.infoTitle || "",
    infoTime: row.infoTime || "",
    infoChildType: row.infoChildType || "",
    longitude: row.infoLongitude || "",
    latitude: row.infoLatitude || "",
    eventDetail: row.eventInfo || "",
    deathCount: row.deathNum || 0,
    missingCount: row.missingNum || 0,
    seriousInjuryCount: row.severeInjuryNum || 0,
    minorInjuryCount: row.lightInjuryNum || 0,
    reportMethod: row.infoReportingMethod || "",
    locationPath: (() => {
      if (row.infoDistrict && row.infoTownshipStreet) {
        return [row.infoDistrict, row.infoTownshipStreet];
      } else if (row.infoDistrict) {
        return [row.infoDistrict];
      } else {
        return [];
      }
    })(),
    reportUnit: row.infoReportingUnit || "",
    reportPerson: row.infoReportingUser || "",
    attentionStatus: row.attentionStatus || 0,
    infoStatus: row.infoStatus || 0,
    status: row.status || 0,
  };
};

// 填充续保表单数据（续保模式 - 只保留基本信息）
export const fillContinueFormWithRowData = (row) => {
  // 改进地点数据处理 - 直接提取所有可能的地点字段
  const getLocationPath = () => {
    const locationData = {
      infoDistrict: row.infoDistrict,
      infoTownshipStreet: row.infoTownshipStreet,
      infoTownshipStreetDisplay: row.infoTownshipStreetDisplay,
      // 其他可能的字段
      district: row.district,
      street: row.street,
      districtId: row.districtId,
      streetId: row.streetId,
    };

    // 构建地点路径 - 优先使用ID字段
    let locationPath = [];

    // 第一优先级：使用 infoDistrict 和 infoTownshipStreet
    if (row.infoDistrict) {
      locationPath.push(row.infoDistrict);
      if (row.infoTownshipStreet) {
        locationPath.push(row.infoTownshipStreet);
      }
      return locationPath;
    }
    // 第二优先级：使用其他字段
    else if (row.district || row.districtId) {
      const districtValue = row.district || row.districtId;
      locationPath.push(districtValue);
      if (row.street || row.streetId) {
        const streetValue = row.street || row.streetId;
        locationPath.push(streetValue);
      }
      return locationPath;
    }

    if (locationPath.length === 0 && row.infoTownshipStreetDisplay) {
      return []; // 实际的ID匹配需要在组件中处理
    }

    return [];
  };

  const getEventType = () => {
    // 优先使用infoChildType字段，这是表单实际使用的字段
    let eventType = row.infoChildType || "";

    // 如果infoChildType为空，尝试使用infoType字段
    if (!eventType) {
      eventType = row.infoType || "";
    }

    return eventType;
  };

  const locationPath = getLocationPath();
  const eventType = getEventType();

  const formData = {
    // 基本信息保留
    infoTitle: row.infoTitle || "",
    infoTime: row.infoTime || "",
    infoChildType: eventType,
    locationPath: locationPath,
    // 保存原始地点名称，用于在组件中进行匹配
    locationDisplayName: row.infoTownshipStreetDisplay || "",
    longitude: row.infoLongitude || row.longitude || "",
    latitude: row.infoLatitude || row.latitude || "",

    // 其他信息清空，需要手动填写
    eventDetail: "",
    deathCount: 0,
    missingCount: 0,
    seriousInjuryCount: 0,
    minorInjuryCount: 0,
    reportMethod: "",
    reportUnit: "",
    reportPerson: "",
    attentionStatus: 0,
    infoStatus: 0,
    status: 0,
  };

  return formData;
};

// 格式化续报提交数据
export const formatContinueSubmitData = (form, reportInfoId) => {
  const data = {
    reportInfoId: reportInfoId, // 原接报信息ID
    eventTitle: form.infoTitle || "",
    eventInfo: form.eventDetail || "",

    // 伤亡统计
    deathNum: parseInt(form.deathCount) || 0,
    missingNum: parseInt(form.missingCount) || 0,
    severeInjuryNum: parseInt(form.seriousInjuryCount) || 0,
    lightInjuryNum: parseInt(form.minorInjuryCount) || 0,

    // 报送信息
    eventReportingUnit: form.reportUnit || "",
    eventReportingUser: form.reportPerson || "",
    eventReportingMethod: form.reportMethod || "",

    // 地理位置信息
    eventDistrict:
      form.locationPath && form.locationPath.length > 0
        ? form.locationPath[0]
        : "",
    eventTownshipStreet:
      form.locationPath && form.locationPath.length > 1
        ? form.locationPath[1]
        : "",
    eventLongitude: form.longitude || "",
    eventLatitude: form.latitude || "",
    eventLocationDetail: form.locationDetail || "",

    // 事件类型
    eventType: form.infoType || form.infoChildType || "",
    eventChildType: form.infoChildType || "",

    // 时间信息
    eventTime: form.infoTime || "",

    processType: 2, // 续报类型
    isDel: 0,
  };

  // 时间格式化处理
  if (data.eventTime) {
    const date = new Date(data.eventTime);
    if (!isNaN(date.getTime())) {
      data.eventTime = date.toISOString().slice(0, 19).replace("T", " ");
    }
  }

  return data;
};

// 构建查询参数
export const buildQueryParams = (searchParams, pagination) => {
  const formatToISO = (dateStr) => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? "" : date.toISOString();
  };

  return {
    page: pagination.currentPage,
    count: pagination.pageSize,
    infoEndTime: searchParams.infoTime?.[1]
      ? formatToISO(searchParams.infoTime[1])
      : "",
    infoStartTime: searchParams.infoTime?.[0]
      ? formatToISO(searchParams.infoTime[0])
      : "",
    infoTitle: searchParams.infoTitle || "",
    infoType: searchParams.infoType || "",
    status: searchParams.status || "",
  };
};
