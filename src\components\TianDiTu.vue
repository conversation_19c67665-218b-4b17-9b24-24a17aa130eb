<template>
  <div id="map-container"></div>
</template>

<script>
export default {
  name: "TianDiTu",
  data() {
    return {
      map: null,
    };
  },
  methods: {
    initMap() {
      var T = window.T;
      this.map = new T.Map("map-container", {
        projection: "EPSG:4326",
      });
      this.map.centerAndZoom(new T.LngLat(116.40769, 39.89945), 13);
    },
    addMarker(Lng, Lat) {
      if (typeof Lng !== "number" || typeof Lat !== "number") {
        return;
      }
      this.map.clearOverLays();
      var marker = new T.Marker(new T.LngLat(Lng, Lat));
      this.map.addOverLay(marker);
      this.map.panTo(new T.LngLat(Lng, Lat));
    },
  },
  mounted() {
    this.initMap();
  },
};
</script>

<style>
#map-container {
  width: 100%;
  height: 100%;
}
</style>