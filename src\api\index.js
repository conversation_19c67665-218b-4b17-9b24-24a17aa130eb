/**
 * API接口统一导出
 */

// 导入各个模块的API
import contactApi from "./contact";
import orgApi from "./organization";
import userApi from "./user";
import positionApi from "./position";
import logApi from "./log";
import textMessageTemplateApi from "./textMessageTemplate";
import textMessageManagementApi from "./textMessageManagement";
import textMessageRecordApi from "./textMessageRecord";
import textMessageDraft<PERSON>pi from "./textMessageDraft";
import textMessageSuffixApi from "./textMessageSuffix";
import textMessageSendApi from "./textMessageSend";
import systemManagementApi from "@/api/systemManagement";
import dutyManagementApi from "./dutyManagement";
import emergencyKnowledgeBaseApi from "./emergencyKnowledgeBase";
import warningPublishAccessApi from "./warningPublishAccess";
import receiveInformationApi from "./receiveInformation";
import comprehensiveAssessmentApi from "./comprehensiveAssessment";
import emergencyEventInfoApi from "./emergencyEventInfo";
import publicSentimentApi from "./publicSentiment/index"; //舆情态势
import leadershipDirectivesApi from "./leadershipDirectives";
import meetingManagementApi from "./meetingManagement";
import warningPublishApi from "./warningPublish";
import systemConfigApi from "./systemConfig";
import commandConfigurationApi from "./commandConfiguration";

// 统一导出API模块
export {
  contactApi,
  orgApi,
  userApi,
  positionApi,
  logApi,
  textMessageTemplateApi,
  textMessageManagementApi,
  textMessageRecordApi,
  textMessageDraftApi,
  textMessageSuffixApi,
  textMessageSendApi,
  systemManagementApi,
  dutyManagementApi,
  emergencyKnowledgeBaseApi,
  warningPublishAccessApi,
  receiveInformationApi,
  comprehensiveAssessmentApi,
  emergencyEventInfoApi,
  publicSentimentApi,
  leadershipDirectivesApi,
  meetingManagementApi,
  warningPublishApi,
  systemConfigApi,
  commandConfigurationApi,
};
