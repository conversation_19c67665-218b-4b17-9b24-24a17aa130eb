<template>
  <div class="user-management-container">
    <!-- 用户管理 - Page -->

    <div class="user-management-main">
      <!-- 左侧树形结构 -->
      <div class="left-panel-container" :class="{ collapsed: isTreeCollapsed }">
        <div class="tree-header">
          <el-button type="text" @click="toggleTree" class="collapse-btn">
            <i
              :class="isTreeCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"
            ></i>
          </el-button>
        </div>
        <div
          v-show="!isTreeCollapsed"
          class="tree-content"
          v-loading="treeLoading"
          element-loading-text="加载组织架构中..."
          element-loading-spinner="el-icon-loading"
        >
          <TreePanel
            :tree-data="orgTree"
            :show-search="true"
            :show-actions="false"
            :expand-all="true"
            :treeProps="propsObj"
            @node-click="handleOrgClick"
          />
        </div>
      </div>
      <!-- 右侧内容区域 -->
      <portal-table
        style="width: 100%"
        :showAddButton="false"
        :showSelection="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="name"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />

      <!-- 新增编辑 -->
      <addEdit
        v-if="showAdd"
        ref="addEditShow"
        :orgTree="orgTree"
        @confirm="handleSubmit"
      />
      <!-- 分配角色 -->
      <allocationForm
        v-if="showAllocation"
        ref="allocationShow"
        @confirm="handleSubmit"
      />
      <!-- 导入用户 -->
       <UploadFileDialog
        ref="uploadFileDialogRef"
        :uploadTitle="uploadTitle"
        :visible="uploadVisible"
        @handle-import-file="handleImportFile"
        @success="handleImportSuccess"
        @cancel="handleImportCancel"
      />
    </div>
  </div>
</template>

<script>
import TreePanel from "@/components/TreePanel.vue";
import SearchForm from "@/components/SearchForm.vue";
import DataTable from "@/components/DataTable.vue";
import { mapState, mapActions } from "vuex";
import { orgApi, systemManagementApi } from "@/api/index";
import PortalTable from "@/components/PortalTable/index.vue";

import { getItemList, userPositionDictionaryId } from "@/utils/dictionary";
import allocationForm from "./components/allocationForm.vue";
import addEdit from "./components/addEdit.vue";
import UploadFileDialog from "@/components/UploadFileDialog.vue";


export default {
  name: "UserManagement",
  components: {
    TreePanel,
    SearchForm,
    DataTable,
    PortalTable,
    // GeneralDialog,
    addEdit,
    allocationForm,
    UploadFileDialog,
  },
  data() {
    return {
      uploadTitle: "导入用户列表",
      uploadVisible: false,
      treeLoading: false,
      showAdd: false,
      showAllocation: false,
      orgId: "",
      orgTree: [],
      formData: {},
      propsObj: {
        label: "orgName",
      },
      tableData: [],
      columns: [
        { text: true, prop: "orgName", label: "所属机构" },
        { text: true, prop: "loginName", label: "账号名称" },
        { text: true, prop: "userName", label: "用户姓名" },
        { text: true, prop: "phone", label: "联系电话（手机）" },
        { text: true, prop: "phoneTel", label: "联系电话（座机）" },
        { text: true, prop: "positions", label: "用户职务" },
        { text: true, prop: "userClass", label: "用户分级" },
        { text: true, prop: "statusNmae", label: "账号状态" },
        { text: true, prop: "updateTime", label: "更新日期" },
        { text: true, prop: "remark", label: "用户描述" },

        {
          action: true,
          label: "操作",
          width: 160,
          operationList: [
            {
              label: "查看",
              permission: "user:view",
              buttonClick: this.handleView,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "更多",
              buttonClick: "",
              isShow: (row, $index) => {
                return true;
              },
              children: [
                {
                  label: "编辑",
                  permission: "user:edit",
                  buttonClick: this.handleEdit,
                  isShow: (row, $index) => {
                    return true;
                  },
                },
                {
                  label: "审核",
                  permission: "user:audit",
                  buttonClick: this.toExamine,
                  isShow: (row, $index) => {
                    return row.status == 0;
                  },
                },
                {
                  label: "分配角色",
                  permission: "user:assignRole",
                  buttonClick: this.allocation,
                  isShow: (row, $index) => {
                    return true;
                  },
                },
                {
                  label: "启用",
                  permission: "user:enable",
                  buttonClick: this.stateFn,
                  isShow: (row, $index) => {
                    return row.status == 3;
                  },
                },
                {
                  label: "注销",
                  permission: "user:disable",
                  buttonClick: this.stateFn,
                  isShow: (row, $index) => {
                    return row.status == 1;
                  },
                },
              ],
            },
          ],
        },
      ],
      searchItems: [
        {
          prop: "loginName",
          label: "账号名称",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "userName",
          label: "用户名称",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "phone",
          label: "联系电话",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "orgName",
          label: "所属机构",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "remark",
          label: "用户描述",
          type: "input",
          placeholder: "请输入",
        },
        {
          prop: "positions",
          label: "用户职务",
          type: "select",
          placeholder: "请选择",
          options: [],
        },
        {
          prop: "status",
          label: "账号状态",
          type: "select",
          placeholder: "请选择",
          options: [
            { label: "全部", value: "" },
            { label: "正常", value: 1 },
            { label: "注销", value: 3 },
          ],
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 树形面板折叠状态
      isTreeCollapsed: false,

      // 表格刷新key
      tableKey: 0,
      selectedOrgId: null,
    };
  },
  computed: {
    ...mapState("userManagement", [
      "userList",
      // "pagination",
      "loading",
      "selectedOrg",
    ]),

    // 响应式表格列配置
    responsiveTableColumns() {
      const screenWidth = window.innerWidth;
      if (screenWidth < 1200) {
        // 小屏幕时隐藏部分列
        return this.tableColumns.filter((col) =>
          ["name", "phone", "department", "remark"].includes(col.prop)
        );
      }
      return this.tableColumns;
    },
  },
  created() {
    this.queryOrgTreeFn();
    this.dictionaryFn(userPositionDictionaryId, 1); //用户等级
    this.tebDataFn({
      count: this.pagination.pageSize,
      page: this.pagination.currentPage,
    });
  },
  mounted() {
    this.registerHandlers();
    // 监听窗口大小变化
    window.addEventListener("resize", this.handleResize);
  },

  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    async tebDataFn(params) {
      const res = await orgApi.querySysUserList(params);
      const { code, data, message } = res;
      if (code === 0) {
        if (data.items.length > 0) {
          data.items.forEach((item) => {
            item.statusNmae =
              item.status == 1
                ? "正常"
                : item.status == 3
                ? "注销"
                : item.status == 0
                ? "待审核"
                : "驳回";
          });
        }
        this.tableData = data.items;
        this.pagination.total = data.total;
      } else {
        this.$message.error(message);
      }
    },

    async queryOrgTreeFn() {
      this.treeLoading = true;
      const res = await orgApi.queryOrgTree();
      const { code, data, message } = res;
      if (code === 0) {
        this.treeLoading = false;
        this.orgTree = data;
      } else {
        this.treeLoading = false;
        this.$message.error(message);
      }
    },

    addHandler() {
      this.showAdd = true;
      const obj = {
        formType: 1, //1添加 2编辑
        orgTree: JSON.parse(JSON.stringify(this.orgTree)), //组织树结构
      };
      this.$nextTick(() => {
        this.$refs.addEditShow.addEditShowFn(obj);
      });
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "download_top",
        handler: this.handleDownloadTemplate,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "import_top",
        handler: this.handleImport,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "export_top",
        handler: this.handleExport,
      });
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
    },

    // 下载模版
    handleDownloadTemplate() {
      this.downloadFile(1);
    },

    // 导出按钮点击
    handleExport() {
      this.downloadFile(2);
    },

    async downloadFile(type) {
      let response;
      let fileName = "";
      if (type === 1) {
        // 下载模板
        fileName = "用户信息模版.xlsx";
        response = await systemManagementApi.systemDownloadTemplate(2);
      } else {
        // 导出数据
        fileName = "用户信息表格.xlsx";
        response = await orgApi.downSysUserInfoExcel({orgId: this.selectedOrgId, orgName: this.searchKeyword});
      }
      var blob = new Blob([response.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }); // application/vnd.openxmlformats-officedocument.spreadsheetml.sheet这里表示xlsx类型
      var downloadElement = document.createElement('a');
      var href = window.URL.createObjectURL(blob); // 创建下载的链接
      downloadElement.href = href;
      downloadElement.download = fileName; // 下载后文件名
      document.body.appendChild(downloadElement);
      downloadElement.click(); // 点击下载
      document.body.removeChild(downloadElement); // 下载完成移除元素
      window.URL.revokeObjectURL(href); // 释放掉blob对象
    },

    handleImport() {
      // 导入
      this.uploadVisible = true;
    },
    async handleImportFile(formData) {
      // 开始导入
      try {
        const response = await orgApi.importSysUserInfoExcel(formData);
        if (response && response.code === 0) {
          this.handleImportSuccess();
        } else {
          this.$nextTick(function () {
            this.$refs.uploadFileDialogRef.handleResponseResult(response);
          })
        }
      } catch (error) {
        this.$nextTick(function () {
          this.$refs.uploadFileDialogRef.handleResponseResult(error);
        })
      }
    },
    handleImportSuccess() {
      // 导入成功
      this.uploadVisible = false;
      this.pagination.pageSize = 10;
      this.pagination.currentPage = 1;
      this.tebDataFn({
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
      });
    },
    handleImportCancel() {
      // 导入失败
      this.uploadVisible = false;
    },

    async initData(orgId) {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        orgId: orgId,
      };
      this.orgId = orgId;
      this.tebDataFn(params);
    },

    // 组织架构相关方法
    handleOrgClick(data) {
      this.$store.commit("userManagement/SET_SELECTED_ORG", data);
      // 根据组织筛选用户
      this.filterUsersByOrg(data.id);
    },

    // 搜索相关方法
    async handleSearch(formData) {
      this.pagination.currentPage = 1;
      this.pagination.pageSize = 10;
      const params = {
        // orgId:this.orgId,
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...formData,
      };
      this.formData = formData;
      this.tebDataFn(params);
    },

    async handleReset() {
      // 重置搜索条件
      this.$store.commit("userManagement/SET_SEARCH_PARAMS", {});
      this.$store.commit("userManagement/SET_SELECTED_ORG", null);
      this.$store.commit("userManagement/SET_PAGINATION", { current: 1 });
      await this.getUserList();
      this.$message.success("重置完成");
    },

    // 分页相关方法
    async handleSizeChange(size) {
      this.pagination.currentPage = 1;
      this.pagination.pageSize = size;
      var params = {
        count: size,
        page: 1,
        // orgId:this.orgId,
        ...this.formData,
      };
      this.tebDataFn(params);
    },

    async handleCurrentChange(page) {
      this.pagination.currentPage = page;
      var params = {
        count: this.pagination.pageSize,
        page: page,
        // orgId:this.orgId,
        ...this.formData,
      };
      this.tebDataFn(params);
    },

    handleSortChange({ column, prop, order }) {
      console.log("排序变化:", { column, prop, order });
      // 这里可以实现排序逻辑
    },

    async filterUsersByOrg(orgId) {
      this.selectedOrgId = orgId;
      this.initData(orgId);
    },

    // 切换树形面板折叠状态
    toggleTree() {
      this.isTreeCollapsed = !this.isTreeCollapsed;
    },

    // 处理窗口大小变化
    handleResize() {
      // 强制重新计算响应式列配置
      this.$forceUpdate();
    },

    // 处理行操作按钮点击
    handleRowAction(action, row, index) {
      console.log("行操作:", action.key, row, index);
      switch (action.key) {
        case "view":
          this.handleView(row);
          break;
        case "edit":
          this.handleEdit(row);
          break;
        case "audit":
          this.handleAudit(row);
          break;
        case "assignRole":
          this.handleAssignRole(row);
          break;
      }
    },

    handleView(row) {
      //查看
      this.showAdd = true;
      const obj = {
        formType: 3, //1添加 2编辑 3查看
        row: row,
        orgTree: JSON.parse(JSON.stringify(this.orgTree)), //组织树结构
      };
      this.$nextTick(() => {
        this.$refs.addEditShow.addEditShowFn(obj);
      });
    },

    toExamine(row) {
      //审核
      this.$confirm("审核用户", "提示", {
        confirmButtonText: "通过",
        cancelButtonText: "驳回",
        distinguishCancelAndClose: true, //区分关闭与取消按钮
        type: "warning",
      })
        .then(() => {
          this.auditUserInfoFn(row, 1);
        })
        .catch((action) => {
          if (action === "cancel") {
            this.auditUserInfoFn(row, 2);
          }
        });
    },

    async auditUserInfoFn(row, type) {
      //审核提交
      const obj = {
        id: row.id,
        status: type,
      };
      const res = await orgApi.auditUserInfo(obj);
      const { code, message, error } = res;
      if (code === 0) {
        if (type == 1) {
          this.$message.success("审核通过");
        } else {
          this.$message.error("已驳回");
        }
        this.initData(row.orgId);
      } else {
        this.$message.error(message || error);
      }
    },

    allocation(row) {
      //分配角色
      this.showAllocation = true;
      const obj = {
        formType: 4, //1 添加 2 编辑 3查看 4分配角色
        row: row,
      };
      this.$nextTick(() => {
        this.$refs.allocationShow.addEditShowFn(obj);
      });
    },

    async dictionaryFn(dataObj) {
      const data = await getItemList(dataObj);
      this.searchItems[5].options = data;
      this.searchItems[5].options.forEach((item) => {
        item.label = item.itemName;
        item.value = item.id;
      });
    },

    stateFn(row) {
      //启用 注销
      if (row.status == 3) {
        this.$confirm("确认启用该用户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.startSysUserFn(row, 3);
        });
      } else {
        this.$confirm("确认注销该用户吗?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          this.startSysUserFn(row, 4);
        });
      }
    },
    async startSysUserFn(row, type) {
      const obj = {
        id: row.id,
      };
      var res;
      if (type == 3) {
        //启用
        res = await orgApi.startSysUser(obj);
      } else {
        res = await orgApi.disableSysUser(obj);
      }
      const { code, message, error } = res;
      if (code === 0) {
        if (type == 3) {
          this.$message.success("启用成功");
        } else {
          this.$message.error("已注销");
        }
        this.initData(row.orgId);
      } else {
        this.$message.error(message || error);
      }
    },

    // 编辑
    handleEdit(row) {
      this.showAdd = true;
      const obj = {
        formType: 2, //1添加 2编辑
        row: row,
        orgTree: JSON.parse(JSON.stringify(this.orgTree)), //组织树结构
      };
      this.$nextTick(() => {
        this.$refs.addEditShow.addEditShowFn(obj);
      });
    },

    // 删除
    handleAudit(row) {
      this.$message.info(`审核用户: ${row.name}`);
      // TODO: 删除当前组织机构
    },

    //提交
    handleSubmit(orgId) {
      this.initData(orgId);
    },

    //取消
    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        focusName: "", //重点类型名称
        focusCode: "", //重点类型代码
        remark: "", //备注
      };
    },
  },
};
</script>

<style scoped>
.user-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-management-main {
  flex: 1;
  display: flex;
  gap: 8px;
  padding: 8px;
}

.left-panel-container {
  width: 260px;
  flex-shrink: 0;
  transition: width 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 160px);
  min-width: 40px;
  overflow: hidden;
  max-width: 260px;
}

.left-panel-container.collapsed {
  width: 40px;
  min-width: 40px;
}

.tree-header {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--content-bg);
  border: none;
}

.collapse-btn {
  padding: 8px;
  color: #606266;
  font-size: 16px;
}

.collapse-btn:hover {
  color: #409eff;
}

.tree-content {
  flex: 1;
  overflow: hidden;
  height: calc(100% - 40px);
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.search-wrapper {
  margin-bottom: 8px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 操作按钮样式 */
.table-wrapper >>> .el-button--text.el-button--mini {
  margin-right: 2px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
  min-width: auto !important;
  white-space: nowrap !important;
  line-height: 1.4 !important;
}

/* 最后一个按钮不需要右边距 */
.table-wrapper >>> .el-button--text.el-button--mini:last-child {
  margin-right: 0 !important;
}

/* 查询按钮 - 使用Element UI primary颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(1) {
  color: #409eff !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(1):hover {
  color: #66b1ff !important;
}

/* 编辑按钮 - 使用Element UI success颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(2) {
  color: #67c23a !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(2):hover {
  color: #85ce61 !important;
}

/* 审核按钮 - 使用Element UI warning颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(3) {
  color: #e6a23c !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(3):hover {
  color: #ebb563 !important;
}

/* 分配角色按钮 - 使用Element UI info颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(4) {
  color: #909399 !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(4):hover {
  color: #a6a9ad !important;
}

/* 修改搜索公共组件样式 */
.search-wrapper ::v-deep .search-form .search-content {
  display: flex;
  align-items: center;
  gap: 16px;
}
.search-wrapper ::v-deep .search-form .search-content .search-row {
  margin-bottom: 0 !important;
}
.search-wrapper
  ::v-deep
  .search-form
  .search-content
  .search-row
  .search-item
  .el-input {
  min-width: 400px !important;
}
.search-wrapper ::v-deep .search-form .search-content .search-row {
  margin-bottom: 0 !important;
}
.search-wrapper ::v-deep .search-form .search-content .search-buttons-row {
  margin: 0px !important;
}
/* 修改搜索公共组件样式 */
</style>
