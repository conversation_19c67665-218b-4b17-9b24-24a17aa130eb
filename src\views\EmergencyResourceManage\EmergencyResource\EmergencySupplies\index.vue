<!-- 应急物资 EmergencySupplies -->
<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="物资名称" prop="equipmentName">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.equipmentName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="规格型号" prop="specificationModel">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.specificationModel" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="物资类型" prop="equipmentType">
          <el-select :disabled="styleType === 3" v-model="form.equipmentType" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in equipmentTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="应用领域" prop="applicationArea">
          <el-select :disabled="styleType === 3" v-model="form.applicationArea" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in applicationAreaList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="物资状态" prop="combatReadinessStatus">
          <el-select :disabled="styleType === 3" v-model="form.combatReadinessStatus" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in combatReadinessStatusList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="物资所在地区" prop="depositLocation">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.depositLocation" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="经度" prop="teamLongitude">
          <el-input :disabled="styleType === 3" type="text" v-model="form.teamLongitude" autocomplete="off" placeholder="请输入"
            style="width: 160px"></el-input>  例如:116.4074
        </el-form-item>
        <el-form-item label="纬度" prop="teamLatitude" >
          <el-input :disabled="styleType === 3" type="text" v-model="form.teamLatitude" autocomplete="off" placeholder="请输入"
            style="width: 160px"></el-input>  例如:39.9042
        </el-form-item>

        <el-form-item label="物资存放地点" prop="depositLocationDistrict">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.depositLocationDistrict" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="数量" prop="equipmentNumber">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.equipmentNumber" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="所属队伍" prop="teamId">
          <el-select :disabled="styleType === 3" v-model="form.teamId" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in teamList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="联系人" prop="contacts">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.contacts" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="联系电话" prop="contactsPhoneNumber">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.contactsPhoneNumber" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="物资品牌" prop="brand">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.brand" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="购置年份" prop="purchaseTime">
          <el-date-picker
            :disabled="styleType === 3"
            v-model="form.purchaseTime"
            format="yyyy"
            placeholder="选择年份"
            style="width: 184px;"
            type="year"
            @change="handleChangeMonth">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="物资来源" prop="configurationMethodId">
          <el-select :disabled="styleType === 3" v-model="form.configurationMethodId" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in configurationMethodIdList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </general-dialog>

    <!-- <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { emergencyKnowledgeBaseApi } from "@/api";
import 
{ 
  getItemList, 
  equipmentType, 
  applicationArea, 
  combatReadinessStatus,
  configurationMethodId,
  teamLevelType
 } from "@/utils/dictionary";

import { getCurrentDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "EmergencySupplies",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "equipmentName",
          label: "物资名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "equipmentType",
          label: "物资类型",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "applicationArea",
          label: "应用领域",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "teamName",
          label: "所属队伍",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "teamLevel",
          label: "队伍等级",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "configurationMethodId",
          label: "物资来源",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "equipmentName", label: "物资名称", text: true },
        { prop: "equipmentType", label: "物资类型", text: true },
        { prop: "applicationArea", label: "应用领域", text: true },
        { prop: "equipmentNumber", label: "数量", text: true },
        { prop: "combatReadinessStatus", label: "物资状态", text: true },
        { prop: "brand", label: "物资品牌", text: true },
        { prop: "purchaseTime", label: "购置年份", text: true },
        { prop: "configurationMethodId", label: "物资来源", text: true },
        { prop: "teamName", label: "所属队伍", text: true },
        { prop: "contacts", label: "联系人", text: true },
        { prop: "contactsPhoneNumber", label: "联系电话", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '230px',
          operationList: [
            {
              label: '查看',
              permission: 'supplies:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'supplies:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'supplies:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      trueOrFalseTypeList:[
        {itemName: "是", id: "0"},
        {itemName: "否", id: "1"}
      ],
      equipmentTypeList: [],
      applicationAreaList:[],
      combatReadinessStatusList:[],
      teamList: [],
      configurationMethodIdList: [],

      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增应急物资",

      form: {
        equipmentName: "",
        specificationModel: "",
        equipmentType: "",
        applicationArea: "",
        combatReadinessStatus: "",
        depositLocation: "",
        teamLongitude: "",
        teamLatitude: "",
        depositLocationDistrict: "",
        equipmentNumber: 0,
        teamId: "",
        contacts: "",
        contactsPhoneNumber: "",
        brand: "",
        purchaseTime: "",
        configurationMethodId: "",
        // fileList:[]
      },
      rules: {
        equipmentName: [
          {required: true, message: '物资名称不能为空', trigger: 'blur'}
        ],
        specificationModel: [
          {required: true, message: '规格型号不能为空', trigger: 'blur'}
        ],
        equipmentType: [
          {required: true, message: '物资类型不能为空', trigger: 'blur'}
        ],
        applicationArea: [
          {required: true, message: '应用领域不能为空', trigger: 'blur'}
        ],
        combatReadinessStatus: [
          {required: true, message: '物资状态不能为空', trigger: 'blur'}
        ],
        depositLocation: [
          {required: true, message: '物资所在地区不能为空', trigger: 'blur'}
        ],

        teamLongitude: [
          {required: true, message: '经度不能为空', trigger: 'blur'}
        ],
        teamLatitude: [
          {required: true, message: '纬度不能为空', trigger: 'blur'}
        ],
        depositLocationDistrict: [
          {required: true, message: '物资存放地点不能为空', trigger: 'blur'}
        ],
        equipmentNumber: [
          {required: true, message: '数量不能为空', trigger: 'blur'}
        ],
        contacts: [
          {required: true, message: '联系人不能为空', trigger: 'blur'}
        ],
        contactsPhoneNumber: [
          {required: true, message: '联系电话不能为空', trigger: 'blur'}
        ],
        purchaseTime: [
          {required: true, message: '购置年份不能为空', trigger: 'blur'}
        ],
        configurationMethodId: [
          {required: true, message: '物资来源不能为空', trigger: 'blur'}
        ],
      },
    };
  },
  mounted() {
    // this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleChangeMonth(value) {
      this.form.purchaseTime = getCurrentDate("yyyy",value);
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.equipmentTypeList = await getItemList(equipmentType);
        this.searchItems[1].options = this.equipmentTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.applicationAreaList = await getItemList(applicationArea);
        this.searchItems[2].options = this.applicationAreaList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        const teamLevelTypeList = await getItemList(teamLevelType);
        this.searchItems[4].options = teamLevelTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.configurationMethodIdList = await getItemList(configurationMethodId);
        this.searchItems[5].options = this.configurationMethodIdList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.combatReadinessStatusList = await getItemList(combatReadinessStatus);
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      // this.fileList = [];
      // this.form.fileList = [];
      emergencyKnowledgeBaseApi.getEquipmentInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        // if (data.fileList && data.fileList.length > 0) {
        //   data.fileList.forEach((row) => {
        //     this.fileList.push({
        //       name: row.fileName,
        //       url: this.fileBaseUrl + row.fileUrl,
        //       id: row.id,
        //     });
        //   });
        // } else {
        //   data.fileList = [];
        // }
        this.form = {
          ...data,
        };
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.form = {};
      // this.fileList = [];
      // this.form.fileList = [];
      this.generalDialogTitle = "新增应急物资";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑应急物资";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看应急物资";
    },

    //删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        emergencyKnowledgeBaseApi.delEquipmentInfo({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('删除成功');
          this.handSubmitSuccess();
        })
      })
    },

    // // 通报
    // handleApproval() {
    //   setTimeout(() => {
    //     this.$message.success("报送成功！");
    //   }, 500);
    // },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        type: 1,
        ...this.searchParams
      };
      const res = await emergencyKnowledgeBaseApi.getEquipmentInfoPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            this.form.type = 1;
            const res = await emergencyKnowledgeBaseApi.createEquipmentInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await emergencyKnowledgeBaseApi.updEquipmentInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },

    resetFromData() {
      this.form = {
        equipmentName: "",
        specificationModel: "",
        equipmentType: "",
        applicationArea: "",
        combatReadinessStatus: "",
        depositLocation: "",
        teamLongitude: "",
        teamLatitude: "",
        depositLocationDistrict: "",
        equipmentNumber: 0,
        teamId: "",
        contacts: "",
        contactsPhoneNumber: "",
        brand: "",
        purchaseTime: "",
        configurationMethodId: "",
        // fileList:[]
      };
    },

    // // 新增上传方法
    // uploadFile(file) {
    //   // 文件大小校验
    //   this.fileList = [];
    //   const MAX_SIZE = 100 * 1024 * 1024; // 100MB
    //   if (file.file.size > MAX_SIZE) {
    //     this.$message.error("文件大小超过100MB限制");
    //     this.$refs.uploadRef.clearFiles();
    //     return;
    //   }
    //   const formData = new FormData();
    //   formData.append("file", file.file);

    //   systemManagementApi.uploadFile(formData).then((res) => {
    //     this.$refs.uploadRef.clearFiles();
    //     const { code, data, message, error } = res;
    //     if (code !== 0) return this.$message.error(message || error);
    //     this.$message.success("上传成功");
    //     setTimeout(() => {
    //       const fileUrl = this.fileBaseUrl + data.fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileType = getFileExtension(data.url);

    //       this.form.fileList.push(data);
    //       this.fileList.push({
    //         name: data.fileName,
    //         url: fileUrl,
    //         id: data.id,
    //       });
    //     }, 500);
    //   });
    // },
    // handleRemove(file, fileList) {
    //   this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    // },
    // handlePreview(file) {
    //   let fileUrl = file.url;
    //   const fileExtension = fileUrl.split(".").pop().toLowerCase();
    //   const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
    //   if (!previewableExtensions.includes(fileExtension)) {
    //     // 如果文件类型不支持直接预览，则重新拼接URL
    //     fileUrl ='http://' + getIPAddress() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
    //   } 
    //   window.open(fileUrl, '_blank');
    // },
    // handleExceed(files, fileList) {
    //   this.$message.warning("只能上传一个文件");
    //   // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    // },
    // beforeRemove(file, fileList) {
    //   return this.$confirm(`确定移除 ${file.name}？`);
    // },

    // // 关闭预览框
    // handleCloseIframe() {
    //   this.showIframe = false;
    // },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
