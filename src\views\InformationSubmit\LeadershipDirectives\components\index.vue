<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="90%"
    :before-close="handleClose"
    append-to-body
    fullscreen
    custom-class="leadership-detail-dialgo"
  >
    <!-- 领导批示详情弹框 - Component -->
    <div class="directive-detail" v-loading="loading">
      <div class="detail-layout">
        <!-- 左侧详情信息区域 -->
        <div class="detail-info-section">
          <div class="detail-section">
            <h3 class="section-title">基本信息</h3>
            <div class="detail-item">
              <label>事件标题：</label>
              <span>{{ detailData.infoTitle || detailData.eventTitle }}</span>
            </div>
            <div class="detail-item">
              <label>事件详情：</label>
              <div class="detail-content">
                {{ detailData.recordMessage || detailData.eventDetail }}
              </div>
            </div>
            <div class="detail-item">
              <label>批示信息：</label>
              <div class="detail-content">
                {{ detailData.approvalInfo || detailData.directiveContent }}
              </div>
            </div>
            <div class="detail-item">
              <label>批示时间：</label>
              <span>{{
                detailData.approvalTime || detailData.directiveTime
              }}</span>
            </div>
            <div class="detail-item">
              <label>下发时间：</label>
              <span>{{ detailData.sendTime || detailData.issueTime }}</span>
            </div>
            <div class="detail-item">
              <label>反馈时间：</label>
              <span>{{ detailData.feedbackTime }}</span>
            </div>
            <div class="detail-item">
              <label>批示单位：</label>
              <span>{{ detailData.orgName || detailData.directiveUnit }}</span>
            </div>
            <div class="detail-item">
              <label>批示领导：</label>
              <span>{{
                detailData.userName || detailData.directiveLeader
              }}</span>
            </div>
            <div class="detail-item">
              <label>状态：</label>
              <span>{{ detailData.status || detailData.statusText }}</span>
            </div>
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ detailData.createTime }}</span>
            </div>
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ detailData.updateTime }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧PDF预览区域 -->
        <div class="pdf-preview-section">
          <div class="pdf-header">
            <h3>文件预览</h3>
          </div>
          <div class="pdf-container">
            <iframe
              v-if="detailData.pdfUrl"
              :src="detailData.pdfUrl"
              class="pdf-iframe"
              frameborder="0"
            ></iframe>
            <div v-else class="no-pdf">
              <i class="el-icon-document"></i>
              <p>暂无PDF文件</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { leadershipDirectivesApi } from "@/api";

export default {
  name: "LeadershipDirectivesDetail",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: null,
    },
    detailTitle: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      detailData: {
        // API返回的字段
        infoTitle: "",
        recordMessage: "",
        approvalInfo: "",
        approvalTime: "",
        sendTime: "",
        feedbackTime: "",
        orgName: "",
        userName: "",
        status: "",
        pdfUrl: "",
        // 兼容旧字段
        eventTitle: "",
        directiveContent: "",
        directiveTime: "",
        issueTime: "",
        directiveUnit: "",
        directiveLeader: "",
        statusText: "",
        feedbackContent: "",
      },
    };
  },
  computed: {
    dialogTitle() {
      return this.detailTitle ? `${this.detailTitle} - 详情` : "领导批示详情";
    },
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal && this.detailId) {
        this.fetchDetailData();
      }
    },
    detailId(newVal) {
      if (newVal && this.dialogVisible) {
        this.fetchDetailData();
      }
    },
  },
  methods: {
    async fetchDetailData() {
      if (!this.detailId) return;

      this.loading = true;
      try {
        // 调用真实的API接口
        const response = await leadershipDirectivesApi.queryLeadershipInfo({
          id: this.detailId,
        });

        if (response && response.code === 0 && response.data) {
          // 直接使用API返回的数据
          this.detailData = {
            ...response.data,
            // 兼容旧字段映射
            eventTitle: response.data.infoTitle,
            directiveContent: response.data.approvalInfo,
            directiveTime: response.data.approvalTime,
            issueTime: response.data.sendTime,
            directiveUnit: response.data.orgName,
            directiveLeader: response.data.userName,
            statusText: response.data.status, // API已返回中文状态，直接使用
          };
        } else {
          throw new Error(response?.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情失败:", error);
        this.$message.error("获取详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 状态文本映射
    getStatusText(status) {
      const statusMap = {
        0: "待批示",
        1: "待下发",
        2: "待反馈",
        3: "已反馈",
      };
      return statusMap[status] || status;
    },

    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style scoped>
/* 弹框样式 */
:deep(.leadership-detail-dialog) {
  .el-dialog {
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    padding: 0;
    overflow: hidden;
  }
}

.directive-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 左右布局 */
.detail-layout {
  display: flex;
  height: calc(100vh - 95px); /* 全屏弹框下使用更大高度 */
  gap: 20px;
}

/* 右侧PDF预览区域 */
.pdf-preview-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  height: 100%; /* 全屏下使用100%高度 */
}

.pdf-header {
  background-color: #f5f7fa;
  padding: 15px 20px;
  border-bottom: 1px solid #e4e7ed;
}

.pdf-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.pdf-container {
  flex: 1;
  position: relative;
  background-color: #fff;
  overflow-y: auto; /* 支持垂直滚动 */
  height: calc(100% - 50px); /* 减去header高度 */
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.no-pdf {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.no-pdf i {
  font-size: 48px;
  margin-bottom: 10px;
}

.no-pdf p {
  margin: 0;
  font-size: 14px;
}

/* 左侧详情信息区域 */
.detail-info-section {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  height: 100%; /* 全屏下使用100%高度 */
}

.detail-section {
  margin-bottom: 30px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: flex-start;
}

.detail-item label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 10px;
  flex-shrink: 0;
}

.detail-item span {
  color: #303133;
  word-break: break-all;
  flex: 1;
}

.detail-content {
  background-color: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
  color: #303133;
  white-space: pre-wrap;
  word-break: break-word;
  flex: 1;
  min-height: 60px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-layout {
    flex-direction: column;
    height: auto;
  }

  .pdf-preview-section {
    height: 400px;
    min-height: 300px;
  }
  .no-pdf {
    min-height: 300px;
  }
  .detail-info-section {
    flex: none;
    overflow-y: visible;
    height: auto;
  }
}

@media (max-width: 768px) {
  .detail-item {
    flex-direction: column;
  }

  .detail-item label {
    min-width: auto;
    margin-bottom: 5px;
  }
  .no-pdf {
    min-height: 300px;
  }
  :deep(.leadership-detail-dialog) {
    .el-dialog {
      width: 95% !important;
      margin: 2.5vh auto !important;
      height: 95vh;
    }
  }
}
</style>
