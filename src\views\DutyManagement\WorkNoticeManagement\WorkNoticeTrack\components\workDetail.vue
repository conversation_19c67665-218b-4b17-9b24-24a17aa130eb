 <template>
  <div class="modal-content">

    <el-divider content-position="left"></el-divider>
      <!-- <hr class="divider"> -->
    <!-- <h3></h3> -->
    <div class="notice-section">
      <ul style="width: 70%;">
        <li>{{ "通知编号：" + noticeDetail.noticeNum }}</li>
        <li>{{ "发送单位：" + noticeDetail.sendUnit}}</li>
        <li>{{ "接收单位：" + noticeDetail.receiveUnit}}</li>
        <li>{{ "发送时间：" + noticeDetail.publishTime}}</li>
        <li>发送状态：<span class="status-sent">{{ noticeDetail.status }}</span></li>
        <!-- <li>发送状态：<span :class="{'status-sent': noticeDetail.status === '2', 'status-unsent': noticeDetail.status === '1'}">{{ noticeDetail.status }}</span></li> -->
      </ul>

      <div style="width: 25%; margin-left: 5%;">
        <h3>阅读情况</h3>
        <ul >
          <li>{{ "总接收单位：" + noticeDetail.totalReceiveUnitCount }}</li>
          <li>已阅读：<span class="status-sent">{{noticeDetail.readCount + '（' + noticeDetail.readPercentage + '）'}}</span></li>
          <li>未阅读：<span class="status-unsent">{{noticeDetail.unreadCount + '（' + noticeDetail.unreadPercentage + '）'}}</span></li>
        </ul>
        <span>已阅读</span>
        <el-progress :text-inside="true" :stroke-width="22" :percentage="readPercentageNum" status="success"></el-progress>
      </div>
      
    </div>
    
    <!-- <hr class="divider"> -->
    <el-divider content-position="left"></el-divider>
    <h3>通知内容</h3>
    <div class="meeting-arrangement-section">
      <div class="contenTitle"> 会议安排 </div>
      <div class="notice-content"> 
        {{ noticeDetail.content }} 
      </div>
    </div>

    <el-divider content-position="left"></el-divider>
    <h3>附件信息</h3>
    <el-upload
      :disabled="true"
      ref="uploadRef"
      class="upload-demo"
      action=""
      :on-preview="handlePreview"
      :on-remove="handleRemove"
      :before-remove="beforeRemove"
      :http-request="uploadFile"
      multiple
      :limit="1"
      :on-exceed="handleExceed"
      :file-list="fileList"
    >
      <!-- <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        只能上传1个PDF文件，且不超过10M
      </div> -->
    </el-upload>

    <el-divider content-position="left"></el-divider>
    <h3>接收单位阅读情况</h3>
    <portal-table
      style="padding: 20px"
      :tableHeight="tableHeight"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :table-data="noticeDetail.receiveUnitReadStatusList"
      row-key="name"
    />


  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import { systemManagementApi, dutyManagementApi, orgApi } from "@/api";

import { conversionDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "workDetail",
  components: {
    PortalTable,
  },
  data() {
    return {
      tableHeight: 220,
      noticeDetail: {
        "content": "",
        "fileInfoList": [
          {
            "createId": "",
            "createTime": "",
            "fileName": "",
            "fileType": "",
            "fileUrl": "",
            "id": "",
            "isDel": 0,
            "knowledgeId": "",
            "updateId": "",
            "updateTime": ""
          }
        ],
        "noticeNum": "",
        "publishTime": "",
        "readCount": 0,
        "readPercentage": "",
        "receiveUnit": "",
        "receiveUnitReadStatusList": [
          {
            "isRead": "",
            "readTime": "",
            "unitName": ""
          }
        ],
        "sendUnit": "",
        "status": "",
        "title": "",
        "totalReceiveUnitCount": 0,
        "unreadCount": 0,
        unreadPercentage: 0,
      },
      readPercentageNum: 0,
      num: "BJYJ/12",
      isSent: true,
      content: "时  间：2025年5月8日（星期四）9时40分\n地  点：在市应急指挥中心二层指挥大厅设主会场，在市有关部门和单位、各区政府设分会场（采用应急视频系统）\n主持人：张三  市应急局领导\n议  程：\n 一、传达市政府主要领导批示指示\n二、市气象局通报气象预报情况\n 三、市水务局、市交通委、市规划自然资源委、市公安交管局，昌平区政府、门头沟区政府，市排水集团分别汇报降雨应对准备情况\n四、相关同志讲话",
      searchItems: [],
      columns: [
        { prop: "unitName", label: "单位名称", text: true },
        { prop: "readTime", label: "阅读时间", text: true },
        { prop: "isRead", label: "状态", tag: true },
      ],
      tableData: [],

      // styleType: 1, //1：新增，2：编辑，3：查看
      // noticeTypeList: [],
      // trueOrFalseTypeList:[
      //   {itemName: "是", id: "0"},
      //   {itemName: "否", id: "1"}
      // ],
      // pushChannelTypeList: [
      //   {itemName: "内部系统推送", id: "1"},
      //   {itemName: "信息推送", id: "2"},
      //   {itemName: "电子邮件推送", id: "3"}
      // ],

      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      // searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    // this.getTableDataList();
    // this.tableHeight = 300;
    // this.queryDictionaryType();
    // this.registerHandlers();
  },
  methods: {
    // registerHandlers() {
    //   this.$store.commit("generalEvent/registerEventHandler", {
    //     type: "add_top",
    //     handler: this.handleAdd,
    //   });
    // },
    handleChangeMonth(value) {
      this.form.inspectionTime = conversionDate(value);
    },
    handleReceiveChange(value) {
      // 选择的接收对象集合
      const checkedNodes = this.$refs.orgCascaderRef.getCheckedNodes(true);
      console.log('xc12090129102',checkedNodes);
      
      this.selectOrgData = checkedNodes.map((node) => node.data);
      console.log('xc12090129102',this.selectOrgData);
      this.form.receiveList =  this.selectOrgData.map((node) => node.id);
      console.log('xc12090129102',this.form.receiveList);
      // this.selectOrgData.forEach(nodes => {
      //     receiveList.push(nodes.id)
      // })
    },
    handleFeedbackChange(value) {
      // 选择的反馈对象集合
      this.form.feedbackList = value;
      console.log("xc99999999", this.form.feedbackList);
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.noticeTypeList = await getItemList(workNoticeType);
        this.searchItems[1].options = this.noticeTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    showDetailData(data) {
      if (data.receiveUnitReadStatusList?.length > 0) {
        this.tableHeight = Math.min(data.receiveUnitReadStatusList?.length , 5) * 34 + 50;
      }
      this.noticeDetail = data;
      this.fileList = [];
      if (data.fileList && data.fileList.length > 0) {
        data.fileList.forEach((row) => {
          this.fileList.push({
            name: row.fileName,
            url: this.fileBaseUrl + row.fileUrl,
            id: row.id,
          });
        });
      }
      data.receiveUnitReadStatusList.forEach((item) => {
        item.tagType = item.isRead === "已阅读" ? "success" : "danger";
      });
      this.readPercentageNum = parseFloat(data.readPercentage);     
    },
  
    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryContentManagePage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },
    

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl ='http://' + getIPAddress() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
      } 
      window.open(fileUrl, '_blank');
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    // // 关闭预览框
    // handleCloseIframe() {
    //   this.showIframe = false;
    // },
  },

};
</script>

<style lang="scss" scoped>
.modal-content {
  padding: 5px 60px;
}

h3 {
  margin-top: 0;
  color: #444;
  font-size: 16px;
  font-weight: 700;
  // padding-bottom: 8px;
  // border-bottom: 1px solid #eee;
}

ul, ol {
  padding-left: 20px;
}

li {
  margin-bottom: 8px;
}

p {
  margin: 8px 0;
}

.divider {
  border: none;
  border-top: 1px solid #eee;
  margin: 15px 0;
}

.notice-section {
  display: flex;
  margin-bottom: 20px;
}

.read-status-section, .meeting-arrangement-section {
  margin-bottom: 20px;
}

.contenTitle {
  // margin-top: 0;
  color: #444;
  font-size: 23px;
  text-align: center;
}

.notice-content {
  // margin-left: 20px;
  // margin-top: 20px;
  margin: 20px;
  // margin-bottom: 40px;
  line-height: 2.2;
  font-size: 15px;
  color: #444;
  white-space: pre-line; // 保留换行符 

  max-height: calc(100% - 100px);
  overflow-y: auto; 
  padding-right: 10px; 
  
  scrollbar-width: thin; 
  scrollbar-color: #ccc #f5f5f5; 
}

.status-sent {
  color: #4CAF50; /* 绿色 */
  font-weight: bold;
}

.status-unsent {
  color: #F44336; /* 红色 */
  font-weight: bold;
}
</style>
