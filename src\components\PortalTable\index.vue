<template>
  <div class="portal-table">
    <!-- 查询条件 -->
    <el-form
      id="searchForm"
      v-if="searchItems && searchItems.length > 0"
      :inline="true"
      class="search-form"
      :class="{ 'is-right': isRight }"
    >
      <el-form-item
        v-for="(item, index) in searchItems"
        :key="index"
        :label="item.label"
      >
        <el-input
          :style="{ width: item.width + 'px' }"
          v-if="item.type === 'input'"
          v-model="searchParams[item.prop]"
          :placeholder="item.placeholder || '请输入'"
          clearable
        />

        <el-select
          :style="{ width: item.width + 'px' }"
          v-if="item.type === 'select'"
          v-model="searchParams[item.prop]"
          clearable
          filterable
          :placeholder="item.placeholder || '请选择'"
        >
          <el-option
            v-for="item in item.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <select-tree
          v-if="item.type === 'selectTree'"
          ref="selectTreeRef"
          v-model="searchParams[item.prop]"
          :tree-options="item.options"
          :default-props="item.defaultProps"
        />

        <!--   日期带时间     -->
        <el-date-picker
          v-if="item.type === 'date'"
          v-model="searchParams[item.prop]"
          type="datetime"
          :placeholder="item.placeholder || '请输入'"
        />
        <!-- 日期和时间范围 -->
        <el-date-picker
          v-if="item.type === 'datetimerange'"
          v-model="searchParams[item.prop]"
          type="datetimerange"
          :placeholder="item.placeholder || '请输入'"
        />

        <!--   日期不带时间     -->
        <el-date-picker
          v-if="item.type === 'dateNoTime'"
          v-model="searchParams[item.prop]"
          type="date"
          :placeholder="item.placeholder || '请输入'"
        />

        <!-- 当条件查询为开始时间和结束时间时，使用此组件且prop为dataList在条件查询方法去拆分 -->
        <div
          class="star-end-picker"
          v-if="item.type === 'startEndPicker'"
          :style="{ width: item.width ? item.width + 'px' : 'auto' }"
        >
          <el-date-picker
            v-model="searchParams[item.prop]"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
          >
          </el-date-picker>
        </div>

        <el-rate
          v-if="item.type === 'rate'"
          show-score
          v-model="searchParams[item.prop]"
          score-template="{value}"
        />

        <div v-if="item.type === 'combinationInput'" class="combination-input">
          <el-input
            v-model="searchParams[item.prop]"
            :placeholder="item.placeholder || '请输入'"
            clearable
          />
          ～
          <el-input
            v-model="searchParams[item.prop1]"
            :placeholder="item.placeholder || '请输入'"
            clearable
          />
        </div>

        <el-cascader
          v-if="item.type === 'cascader'"
          v-model="searchParams[item.prop]"
          :style="{ width: item.width + 'px' }"
          :options="item.options"
          :props="item.props"
          clearable
        >
        </el-cascader>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" @click="handleSearch">
          查询
        </el-button>
        <el-button
          type="primary"
          icon="el-icon-refresh-right"
          plain
          @click="handleReset"
          >重置
        </el-button>
        <el-button
          v-if="
            showAddButton &&
            (hasRole('超级管理员') ||
              hasPagePermission(addPermission, $route.path))
          "
          type="primary"
          :icon="addButtonIcon"
          plain
          @click="handleAdd"
        >
          {{ addButtonText }}
        </el-button>
        <!-- 页面需要添加单独其他页面用不到操作按钮  -->
        <slot v-if="slotShow"></slot>
      </el-form-item>
    </el-form>

    <el-button
      class="add-button"
      :class="{ 'is-right': isRight }"
      v-else-if="
        showAddButton &&
        (hasRole('超级管理员') || hasPagePermission(addPermission, $route.path))
      "
      type="primary"
      :icon="addButtonIcon"
      plain
      @click="handleAdd"
    >
      {{ addButtonText }}
    </el-button>

    <!-- 数据表格 -->
    <el-table
      ref="tableRef"
      v-if="tableVisible"
      style="width: 100%"
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      :data="tableData"
      :stripe="isTableStripe"
      :border="isTableBorder"
      :height="heightTable"
      :max-height="heightTable"
      :row-key="rowKey"
      :tree-props="treeProps"
      :header-cell-class-name="cellClass"
      @row-click="handleRowClick"
      @select="checkboxSelect"
      @selection-change="handleSelectionChange"
      @select-all="selectAll"
    >
      <el-table-column
        :reserve-selection="true"
        v-if="showSelection"
        type="selection"
        width="55"
      />

      <el-table-column
        v-if="showIndex"
        :index="indexMethod"
        align="center"
        :label="showIndexName"
        type="index"
        width="60"
      />

      <template v-for="col in columns">
        <el-table-column
          v-if="col.label === '操作' ? showDropdown : true"
          :key="col.prop"
          :align="col.align || 'center'"
          :fixed="col.fixed || false"
          v-bind="col"
        >
          <template slot-scope="{ row, column, $index }">
            <!-- 普通列 -->
            <span v-if="col.text">
              <el-popover
                v-if="isOverflow(column, row[col.prop], col.width)"
                placement="top-start"
                trigger="hover"
                :content="row[col.prop]"
              >
                <template slot="reference">
                  <span class="text-ellipsis">
                    {{ col.formatter ? col.formatter(row) : row[col.prop] }}
                  </span>
                </template>
              </el-popover>
              <span v-else>{{
                col.formatter ? col.formatter(row) : row[col.prop]
              }}</span>
            </span>

            <span v-if="col.multiline" style="white-space: pre-line">
              {{ row[col.prop] }}
            </span>

            <span v-if="col.active">
              <span v-if="row[col.prop] === 0 || row[col.prop] === '0'">{{
                col.inactiveName || "否"
              }}</span>
              <span v-if="row[col.prop] === 1 || row[col.prop] === '1'">{{
                col.activeName || "是"
              }}</span>
            </span>

            <!--开关列-->
            <el-switch
              v-if="col.switch"
              v-model="row[col.prop]"
              :active-value="col.activeValue"
              :inactive-value="col.inactiveValue"
              @change="handleSwitchChange(row, $index)"
            ></el-switch>

            <!--标签列-->
            <el-tag v-if="col.tag" :type="row.tagType" size="small">
              {{ row[col.prop] }}
            </el-tag>

            <!--图片列-->
            <template v-if="col.image">
              <el-image
                v-for="image in row[col.prop]"
                style="
                  width: 50px;
                  height: 50px;
                  margin-top: 15px;
                  cursor: pointer;
                "
                :src="image.url"
                :preview-src-list="getColumnPreviewSrcList(row, col.prop)"
                fit="cover"
              ></el-image>
            </template>

            <!--操作列-->
            <template
              v-if="col.action && showDropdown"
              v-for="(item, index) in isShowOperationList(
                col.operationList,
                row,
                $index
              )"
            >
              <!--
              isShow 控制操作按钮显示隐藏，实列代码
              isShow: (row, $index) => {
                return true
              },
             -->
              <el-button
                v-if="
                  !item.children &&
                  item.isShow(row, $index) &&
                  (hasRole('超级管理员') ||
                    hasPagePermission(item.permission, $route.path))
                "
                type="text"
                size="small"
                @click="item.buttonClick(row, $index)"
              >
                {{ item.label }}
              </el-button>

              <el-divider
                v-if="
                  item.isShow(row, $index) &&
                  !item.children &&
                  index !==
                    isShowOperationList(col.operationList, row, $index).length -
                      1 &&
                  showDropdown
                "
                direction="vertical"
              ></el-divider>

              <el-dropdown
                v-if="
                  item.children &&
                  item.children.length > 0 &&
                  hasVisibleChildren(item, row, $index)
                "
                trigger="click"
                placement="bottom"
                @command="handleCommand($event, item.children, row, $index)"
              >
                <el-button type="text" size="small">
                  {{ item.label }}
                </el-button>

                <el-dropdown-menu slot="dropdown">
                  <template
                    v-for="(dropdownItem, dropdownIndex) in item.children"
                  >
                    <el-dropdown-item
                      v-if="
                        dropdownItem.isShow(row, $index) &&
                        (hasRole('超级管理员') ||
                          hasPagePermission(
                            dropdownItem.permission,
                            $route.path
                          ))
                      "
                      :key="dropdownIndex"
                      :command="dropdownIndex"
                    >
                      {{ dropdownItem.label }}
                    </el-dropdown-item>
                  </template>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </template>

      <template slot="empty">
        <img src="@/assets/images/table-empty.png" alt="" />
        <span>{{ emptyDescription }}</span>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-if="showPagination"
      popper-class="pagination-popper"
      class="pagination-wrapper"
      :current-page="pagination.currentPage"
      :page-sizes="[5, 10, 20, 50, 100]"
      :page-size="pagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="pagination.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import {
  getTableAllData,
  hasPermission,
  hasRole,
  processingData,
} from "@/utils/portalUtils";
import { hasPagePermission } from "@/utils/permissionManager";
import SelectTree from "@/components/SelectTree/index.vue";

export default {
  name: "PortalTable",
  components: { SelectTree },
  props: {
    // 表格列配置
    columns: {
      type: Array,
      default: () => [],
    },

    // 表格数据
    tableData: {
      type: Array,
      default: () => [],
    },

    //表格高度
    tableHeight: {
      type: [Number, String],
      default: "",
    },

    //是否开启斑马纹
    isTableStripe: {
      type: Boolean,
      default: true,
    },

    //是否开启边框
    isTableBorder: {
      type: Boolean,
      default: false,
    },

    //表格row-key
    rowKey: {
      type: String,
      default: "",
    },

    //表格树形结构配置
    treeProps: {
      type: Object,
      default: () => ({
        children: "children",
      }),
    },

    //是否全选
    isAllSelect: {
      type: Boolean,
      default: true,
    },

    //是否点击行选中
    isClickRowSelect: {
      type: Boolean,
      default: false,
    },

    // 是否显示多选框列
    showSelection: {
      type: Boolean,
      default: true,
    },

    // 是否显示序号列
    showIndex: {
      type: Boolean,
      default: true,
    },

    // 展示序号表头的名称
    showIndexName: {
      type: String,
      default: "序号",
    },

    //查询添加样式查询提交是否显示在最右侧
    isRight: {
      type: Boolean,
      default: false,
    },

    // 查询条件配置
    searchItems: {
      type: Array,
      default: () => [],
    },

    //新增按钮权限
    addPermission: {
      type: String,
      default: "",
    },

    // 新增按钮文字
    addButtonText: {
      type: String,
      default: "新增",
    },

    // 新增按钮图标
    addButtonIcon: {
      type: String,
      default: "el-icon-plus",
    },

    // 是否显示新增按钮
    showAddButton: {
      type: Boolean,
      default: true,
    },

    //是否显示按钮插槽
    slotShow: {
      type: Boolean,
      default: false,
    },

    //分页是否显示
    showPagination: {
      type: Boolean,
      default: true,
    },

    // 分页配置
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        total: 0,
      }),
    },
    //空数据描述文字
    emptyDescription: {
      type: String,
      default: "暂无数据!",
    },

    //是否监听数据源改变，默认是监听
    showWatch: {
      type: Boolean,
      default: true,
    },

    selectAllType: {
      type: Number,
      default: 0,
    },

    // 表格加载状态
    loading: {
      type: Boolean,
      default: false,
    },
    // 表格是否显示
    tableVisible: {
      type: Boolean,
      default: true,
    },
    // 单元格内文本排版样式，是否换行
    multilineWhiteSpace: {
      type: String,
      default: "pre-line",
    },
  },
  data() {
    return {
      searchParams: {},

      ColumnData2: [],
      checkedKeys: false,

      heightTable: null,
      showDropdown: true,

      textWidth: "",
      selectedRows: [], // 选中的行数据
    };
  },
  watch: {
    tableData: {
      immediate: true,
      handler(newVal) {
        if (this.showWatch) {
          processingData([...newVal]);
          this.ColumnData2 = newVal;
        } else {
          this.ColumnData2 = [];
          this.tableData.forEach((item) => {
            this.ColumnData2.push(item);
          });
        }
      },
    },
    tableHeight(newVal) {
      this.handleTableHeightChange(); // 当tableHeight变化时，重新计算高度
    },
  },
  created() {
    this.searchItems.forEach((item) => {
      if (item.type === "select" && item.hasOwnProperty("defaultValue")) {
        this.$set(this.searchParams, item.prop, item.defaultValue);
      }
    });
  },
  mounted() {
    //计算表格高度
    this.handleTableHeightChange();
  },

  methods: {
    hasRole,
    hasPermission,
    hasPagePermission,

    // 检查是否有管理员权限
    isAdmin() {
      return this.hasRole("admin") || this.hasRole("超级管理员");
    },

    //获取图片地址
    getColumnPreviewSrcList(row, prop) {
      const columnImages = [];
      if (row[prop] && row[prop].length > 0) {
        row[prop].forEach((image) => {
          if (image.url) {
            columnImages.push(image.url);
          }
        });
      }
      return columnImages;
    },

    isOverflow(obj, text, width = "100px") {
      // 强制将 text 转换为字符串类型
      const textStr = this.getDisplayText(text);
      if (obj.realWidth !== -1) {
        width = obj.realWidth;
      } else {
        width = obj.width;
      }
      if (!textStr || !width) return false;
      const span = document.createElement("span");
      span.style.visibility = "hidden";
      span.style.whiteSpace = this.multilineWhiteSpace;
      span.style.display = "inline-block";
      span.style.maxWidth = width;
      span.innerHTML = textStr;
      document.body.appendChild(span);
      const isOver = width <= span.clientWidth + 10;
      document.body.removeChild(span);
      return isOver;
    },

    getDisplayText(value) {
      if (value === null && value === "" && value === null) {
        return "";
      }

      if (Array.isArray(value)) {
        return JSON.stringify(value, null, 2);
      }

      if (typeof value === "object" || typeof value === "number") {
        return String(value);
      }

      return value;
    },

    // 操作按钮筛选
    isShowOperationList(operationList, row, index) {
      if (!operationList) {
        return [];
      }
      return operationList.filter((item) => item.isShow(row, index));
    },

    // 子项可见性检查
    hasVisibleChildren(item, row, index) {
      this.showDropdown = item.children.some(
        (child) =>
          child.isShow(row, index) &&
          this.hasPagePermission(child.permission, this.$route.path)
      );
      return item.children.some(
        (child) =>
          child.isShow(row, index) &&
          this.hasPagePermission(child.permission, this.$route.path)
      );
    },
    //隐藏第一列的多选框样式
    cellClass(row) {
      if (row.columnIndex === 0 && !this.isAllSelect) {
        return "disabledCheck";
      }
    },
    //计算表格动态高度
    handleTableHeightChange() {
      this.$nextTick(() => {
        if (
          this.tableHeight === "" ||
          this.tableHeight === null ||
          this.tableHeight === undefined
        ) {
          const searchElement = document.getElementById("searchForm");
          let searchOffsetH = 0;
          if (searchElement) {
            searchOffsetH = searchElement.offsetHeight;
          }
          //获取el-tabs-header元素
          const elTabsHeader =
            document.getElementsByClassName("el-tabs__header");
          /**
           *  50为tab框高度，
           *  40为面包屑，
           *  12面包屑与页面间隙
           *  20查询控件与表格间隙
           *  45表头的高度
           */
          // console.log('xccccc00000',searchOffsetH);

          const gaping = 50 + 40 + 12 + 20 + 45;
          if (this.showPagination) {
            this.heightTable = window.innerHeight - gaping - searchOffsetH - 55;
          } else {
            this.heightTable = window.innerHeight - gaping - searchOffsetH;
          }
        } else {
          console.log(this.tableHeight);
          this.heightTable = parseInt(this.tableHeight);
        }
      });
    },

    indexMethod(index) {
      if (this.pagination) {
        index =
          index +
          1 +
          (this.pagination.currentPage - 1) * this.pagination.pageSize;
        return index;
      } else {
        return index + 1;
      }
    },

    // 行点击处理函数
    handleRowClick(row) {
      if (!this.isClickRowSelect) return;
      this.$refs.tableRef.toggleRowSelection(row, true);
      this.checkboxSelect("", row);
      this.handleSelectionChange(this.$refs.tableRef.selection);
    },

    handleSelectionChange(selection) {
      this.$emit("handle-selection-change", selection);
    },

    selectAll(selection) {
      // 全选/非全选
      let checkedKeys = selection.length > 0;
      this.splite(this.ColumnData2, checkedKeys);
      this.$emit("handle-checkbox-select", selection, this.ColumnData2, true);
      // this.handleSelectionChange();
    },

    splite(data, flag) {
      data.forEach((row) => {
        row.isSelected = flag;
        this.$set(row, "isSelected", flag);
        this.$refs.tableRef.toggleRowSelection(row, flag);
        row.children && this.splite(row.children, flag);
      });
    },

    checkboxSelect(val, row) {
      const thisSelect = !row.isSelected;
      row.isSelected = thisSelect;
      if (!this.isAllSelect) {
        // 取消其他所有行的选中状态
        const allRows = getTableAllData(this.ColumnData2);
        allRows.forEach((item) => {
          if (item !== row) {
            item.isSelected = false;
            this.$refs.tableRef.toggleRowSelection(item, false);
          }
        });
      } else {
        this.$refs.tableRef.toggleRowSelection(row, thisSelect);
        row.children && this.splite(row.children, thisSelect); // 点击父节点，则子勾选
      }
      this.$emit("handle-checkbox-select", val, row, false);
    },

    getCheckList() {
      return getTableAllData(this.ColumnData2).filter(
        (item) => item.isSelected
      );
    },

    handleSwitchChange(row, $index) {
      this.$emit("switch-change", row, $index);
    },

    handleSearch() {
      this.$emit("search", { ...this.searchParams });
    },

    handleReset() {
      this.searchParams = {};
      //当查询条件有下拉树形加上的默认值清空
      if (this.$refs.selectTreeRef) {
        this.$refs.selectTreeRef[0].selectedNode = {};
        this.$refs.selectTreeRef[0].currentLabel = undefined;
        this.$refs.selectTreeRef[0].currentValue = undefined;
      }

      this.handleSearch();
    },

    handleSizeChange(size) {
      this.$emit("update:pagination", { ...this.pagination, pageSize: size });
      this.$emit("handle-size-change", size);
    },

    handleCurrentChange(page) {
      this.$emit("update:pagination", {
        ...this.pagination,
        currentPage: page,
      });
      this.$emit("handle-current-change", page);
    },

    // 新增按钮点击事件
    handleAdd() {
      this.$emit("add");
    },

    // 操作列按钮点击事件处理
    handleCommand(command, list, row, index) {
      list[command].buttonClick(row, index);
    },

    // 新增时间处理方法
    handleStartDateChange(val, endProp) {
      if (val && this.searchParams[endProp]) {
        const start = new Date(val);
        const end = new Date(this.searchParams[endProp]);
        if (start > end) {
          this.$delete(this.searchParams, endProp);
        }
      }
    },

    // 生成结束时间禁用规则
    getEndPickerOptions(startProp) {
      return {
        disabledDate: (time) => {
          if (this.searchParams[startProp]) {
            return (
              time.getTime() <= new Date(this.searchParams[startProp]).getTime()
            );
          }
          return false;
        },
      };
    },
    // 新增焦点事件处理
    handleDateFocus() {},
  },
};
</script>

<style lang="scss" scoped>
@import "~@/styles/variables.scss";

.portal-table {
  background: var(--white);

  .text-ellipsis {
    max-width: 100%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  .search-form.is-right,
  .add-button.is-right {
    float: right;
  }

  .search-form {
    margin-bottom: $radiusTwenty;

    ::v-deep .el-form-item {
      margin-right: ceil($radiusTwenty + $radiusFive);
      margin-bottom: $radiusTen;
    }

    ::v-deep {
      .el-input__inner {
        border-radius: $radiusFive;
      }

      .el-input__inner:focus {
        border-color: var(--themeColor) !important;
      }

      .el-button--primary {
        background: var(--themeColor);
        border-color: var(--themeColor) !important;
      }

      .el-button--primary.is-plain,
      .el-button--primary.is-plain:hover,
      .el-button--primary.is-plain:focus {
        color: var(--themeColor);
        background: var(--lightGray1);
        border-color: var(--themeColor) !important;
      }

      .el-button--success.is-plain,
      .el-button--success.is-plain:hover,
      .el-button--success.is-plain:focus {
        color: #13ce66;
        background: #e7faf0;
        border-color: #a1ebc2;
      }

      .el-button--info.is-plain,
      .el-button--info.is-plain:hover,
      .el-button--info.is-plain:focus {
        color: #909399;
        background: #f4f4f5;
        border-color: #d3d4d6;
      }
    }
  }

  .el-button--primary.is-plain,
  .el-button--primary.is-plain:hover,
  .el-button--primary.is-plain:focus {
    color: var(--themeColor);
    background: var(--lightGray1);
    border-color: var(--themeColor) !important;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }

  .el-pagination ::v-deep .btn-prev {
    margin-left: 20px !important;
  }

  ::v-deep {
    .cell {
      color: var(--black);
      white-space: pre-wrap;
    }

    .disabledCheck .cell .el-checkbox__inner {
      display: none;
    }

    .disabledCheck .cell::before {
      content: "";
      text-align: center;
      line-height: 37px;
    }

    .el-table__empty-text {
      display: flex;
      flex-direction: column;
      align-items: center;

      img {
        width: 120px;
        height: 120px;
      }

      span {
        font-family: Microsoft YaHei UI, Microsoft YaHei UI;
        font-weight: 400;
        font-size: 18px;
        color: rgba(57, 69, 101, 0.52);
        line-height: 23px;
        letter-spacing: 3px;
        text-align: left;
        font-style: normal;
        text-transform: none;
      }
    }

    .add-button {
      margin-bottom: 30px;
    }

    .el-table__header {
      th {
        background: var(--lightGray2) !important;
        color: #323233 !important;
      }
    }

    .el-table::before {
      background-color: transparent;
    }

    //表格边框
    .el-table--border th.is-leaf,
    .el-table--border td {
      border-bottom-width: 1px !important;
      border-color: #e7e7e7 !important;
    }

    .el-table th.is-leaf,
    .el-table td {
      border-bottom-width: 0;
      padding: 5px 0;
    }

    .el-table--enable-row-transition .el-table__body td {
      background: #ffffff !important; // 替换为你需要的颜色值
    }

    .el-table--striped .el-table__body tr.el-table__row--striped td {
      background: var(--lightGray1) !important; // 替换为你需要的颜色值
    }

    .el-checkbox__input .el-checkbox__inner,
    .el-checkbox__input .el-checkbox__inner:hover {
      border-color: #dcdfe6 !important;
    }

    .el-table__fixed-right::before {
      background-color: transparent;
    }

    .el-rate {
      margin-top: 5px;
    }

    .combination-input {
      .el-input {
        width: 100px;
      }
    }

    .el-loading-spinner .el-loading-text,
    .el-loading-spinner i,
    .el-pager li.active,
    .el-pager li:hover {
      color: var(--themeColor);
    }

    // 自定义loading样式
    .el-loading-spinner {
      .el-loading-text {
        color: #606266;
        font-size: 14px;
        margin-top: 15px;
      }

      .circular {
        width: 42px;
        height: 42px;
        animation: loading-rotate 2s linear infinite;
      }

      .path {
        stroke: var(--themeColor);
        stroke-width: 2;
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-linecap: round;
        animation: loading-dash 1.5s ease-in-out infinite;
      }
    }

    @keyframes loading-rotate {
      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes loading-dash {
      0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
      }
      50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
      }
      100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
      }
    }

    .star-end-picker {
      position: relative;
      display: inline-block;
      vertical-align: middle;

      .el-date-editor {
        width: 100%;
        vertical-align: middle;
      }

      .el-range-editor.el-input__inner {
        padding: 3px 10px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        width: 100%;
        height: 40px;
        line-height: 34px;
        display: flex;
        align-items: center;
      }

      .el-range-input {
        line-height: 34px;
        height: 34px;
        vertical-align: middle;
      }

      .el-range-separator {
        color: #606266 !important;
        padding: 0 8px !important;
        line-height: 34px;
        height: 34px;
        display: inline-block !important;
        vertical-align: middle;
        background: transparent !important;
        border: none !important;
        font-size: 14px;
        z-index: 1;
      }

      .el-input__icon {
        line-height: 34px;
        height: 34px;
        display: flex;
        align-items: center;
      }
    }

    .el-switch.is-checked .el-switch__core {
      border-color: var(--themeColor);
      background-color: var(--themeColor);
    }

    .el-select .el-input.is-focus .el-input__inner,
    .el-pagination__sizes .el-input .el-input__inner:hover {
      border-color: var(--themeColor);
    }

    .el-button--text {
      color: var(--themeColor) !important;
    }
  }
}
</style>
<style lang="scss">
.pagination-popper {
  .el-select-dropdown__item.selected {
    color: var(--white);
    background-color: var(--themeColor) !important;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: var(--lightGray1);
  }
}
</style>
