<template>
  <div class="emergency-event-info-container">
    <!-- 突发事件信息 - Page -->
    <portal-table
      ref="portalTableRef"
      :showAddButton="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      :loading="loading"
      row-key="id"
      @search="handleSearch"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @handle-selection-change="handleSelectionChange"
    />

    <!-- 突发事件信息详情查看弹窗 -->
    <receive-information-detail
      :dialog-visible="detailDialogVisible"
      :detail-id="currentDetailId"
      :detail-title="currentDetailTitle"
      @close="handleDetailClose"
    />

    <!-- 突发事件信息续报表单弹窗 -->
    <receive-information-form
      :dialog-visible="dialogVisible"
      :dialog-title="dialogTitle"
      :is-edit-mode="isEditMode"
      :is-continue-mode="isContinueMode"
      :editing-row="editingRow"
      :location-options="locationOptions"
      :event-type-options="eventTypeOptions"
      :report-method-options="reportMethodOptions"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import ReceiveInformationDetail from "../ReceiveInformation/components/ReceiveInformationDetail.vue";
import ReceiveInformationForm from "../ReceiveInformation/components/ReceiveInformationForm.vue";
import {
  emergencyEventInfoApi,
  systemManagementApi,
  receiveInformationApi,
} from "@/api";
import {
  buildQueryParams,
  responseLevelMap,
} from "./utils/emergencyEventUtils";
import { transformLocationData } from "../ReceiveInformation/utils/receiveInfoUtils";

export default {
  name: "EmergencyEventInfo",
  components: {
    PortalTable,
    ReceiveInformationDetail,
    ReceiveInformationForm,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      eventTypeOptions: [],
      searchParams: {},
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 弹框相关数据
      dialogVisible: false,
      dialogTitle: "续报突发事件信息",
      isEditMode: false,
      isContinueMode: false,
      editingRow: null,

      // 详情查看相关
      detailDialogVisible: false,
      currentDetailId: null,
      currentDetailTitle: "",

      // 表单选项数据
      locationOptions: [],
      reportMethodOptions: [],
      searchItems: [
        {
          type: "input",
          prop: "infoTitle",
          label: "事件标题",
          placeholder: "请输入事件标题",
        },
        {
          type: "datetimerange",
          prop: "infoTime",
          label: "事件时间",
          placeholder: "请选择事件时间",
        },
        {
          type: "select",
          prop: "infoType",
          label: "事件类型",
          placeholder: "请选择事件类型",
          options: [{ label: "全部", value: "" }],
        },
      ],
      columns: [
        {
          prop: "infoTitle",
          label: "事件标题",
          text: true,
        },
        {
          prop: "infoTime",
          label: "事件时间",
          text: true,
        },
        {
          prop: "createTime",
          label: "上报时间",
          text: true,
        },
        {
          prop: "infoType",
          label: "事件类型",
          text: true,
        },
        {
          prop: "infoReportingUnit",
          label: "上报单位",
          text: true,
        },
        {
          prop: "infoStatus",
          label: "事件状态",
          text: true,
          formatter: (row) => {
            if (!row) return "";
            const statusMap = {
              0: "正在处置",
              1: "启动响应",
              2: "处置完毕",
            };
            return statusMap[row.infoStatus] || row.infoStatus || "";
          },
        },
        {
          prop: "responseLevel",
          label: "响应级别",
          text: true,
          formatter: (row) => {
            if (!row) return "";
            return (
              responseLevelMap[row.responseLevel] || row.responseLevel || ""
            );
          },
        },

        {
          action: true,
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "查看",
              permission: "emergency:view",
              buttonClick: this.handleView,
              isShow: () => true,
            },
            {
              label: "续报",
              permission: "emergency:report",
              buttonClick: this.handleFollowUp,
              isShow: () => true,
            },
            {
              label: "指挥调度",
              permission: "emergency:dispatch",
              buttonClick: this.handleDispatch,
              isShow: () => true,
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.fetchData();
    this.initDictionaryData();
    this.loadLocationData();
  },
  methods: {
    async initDictionaryData() {
      try {
        // 加载事件类型字典
        const eventTypeResponse = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507031421",
        });
        if (eventTypeResponse.code === 0) {
          this.eventTypeOptions = eventTypeResponse.data.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));

          const searchEventTypeItem = this.searchItems.find(
            (item) => item.prop === "infoType"
          );
          if (searchEventTypeItem) {
            searchEventTypeItem.options = [
              { label: "全部", value: "" },
              ...this.eventTypeOptions,
            ];
          }
        }

        // 加载上报方式字典
        const reportMethodResponse =
          await systemManagementApi.queryItemListById({
            systemDictionaryId: "202507041558",
          });
        if (reportMethodResponse.code === 0) {
          this.reportMethodOptions = reportMethodResponse.data.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        }
      } catch (error) {
        console.error("获取字典数据失败:", error);
        this.$message.error("获取字典数据失败");
      }
    },

    async loadLocationData() {
      try {
        const response = await receiveInformationApi.queryTownsBeijing();

        if (response && response.code === 0 && response.data) {
          const transformedData = transformLocationData(response.data);

          if (transformedData && transformedData.length > 0) {
            this.locationOptions = transformedData;
          } else {
            this.loadFallbackLocationData();
          }
        } else {
          this.loadFallbackLocationData();
        }
      } catch (error) {
        this.loadFallbackLocationData();
      }
    },

    loadFallbackLocationData() {
      this.locationOptions = [];
      this.$message.error("获取区县街道数据失败");
    },

    async fetchData() {
      this.loading = true;
      try {
        const params = buildQueryParams(this.searchParams || {}, {
          currentPage: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
        });

        const response = await emergencyEventInfoApi.queryEmergencyInfoList(
          params
        );
        if (response.code === 0) {
          this.tableData = response.data.items || [];
          console.log(this.tableData, "this.tableData");
          this.pagination.total = response.data.total || 0;
        } else {
          this.$message.error(response.message || "获取数据失败");
        }
      } catch (error) {
        console.error("获取突发事件信息失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    handleSearch(searchParams) {
      this.pagination.currentPage = 1;
      this.searchParams = searchParams;
      this.fetchData();
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData();
    },

    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },

    handleView(row) {
      this.currentDetailId = row.id;
      this.currentDetailTitle = row.infoTitle;
      this.detailDialogVisible = true;
    },

    handleFollowUp(row) {
      this.isEditMode = false;
      this.isContinueMode = true;
      this.editingRow = row;
      this.dialogTitle = "续报突发事件信息";
      this.dialogVisible = true;
    },

    handleDispatch(row) {
      console.log("指挥调度:", row);
      // TODO: 实现指挥调度功能
    },

    handleCancel() {
      this.dialogVisible = false;
      this.isEditMode = false;
      this.isContinueMode = false;
      this.editingRow = null;
    },

    async handleSubmit(submitData) {
      try {
        let response;
        if (this.isContinueMode) {
          // 续报模式：使用续报接口
          response = await receiveInformationApi.createEventProcessInfo(
            submitData
          );
        }

        if (response.code === 0) {
          this.$message.success("续报突发事件信息成功！");
          this.dialogVisible = false;
          this.fetchData();
        } else {
          this.$message.error(response.message || "续报失败");
        }
      } catch (error) {
        console.error("续报失败:", error);
        this.$message.error("提交失败，请重试");
      }
    },

    handleDetailClose() {
      this.detailDialogVisible = false;
      this.currentDetailId = null;
      this.currentDetailTitle = "";
    },
  },
};
</script>

<style scoped>
.emergency-event-info-container {
  padding: 20px;
}
</style>
