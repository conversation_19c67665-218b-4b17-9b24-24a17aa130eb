<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="detailTitle"
    width="95%"
    :before-close="handleClose"
    custom-class="receive-info-detail-dialog"
    top="3vh"
  >
    <!-- 接报信息详情对话框 - Component -->
    <div v-loading="loading" class="detail-content">
      <!-- 顶部基本信息区域 -->
      <div class="header-info">
        <div class="info-items-container">
          <div class="info-item">
            <span class="info-label">事件类型：</span>
            <span class="info-value">{{ detailData.infoType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">所在辖区：</span>
            <span class="info-value">{{ detailData.infoDistrict || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">事发时间：</span>
            <span class="info-value">{{
              formatDate(detailData.infoTime) || "-"
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">事发地点：</span>
            <span class="info-value">{{
              detailData.infoLocationDetail ||
              detailData.infoTownshipStreetDisplay ||
              "-"
            }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">正在处置：</span>
            <span class="info-value status-processing">
              <i class="el-icon-user"></i>
              {{ currentUserName || "系统" }}
            </span>
          </div>
          <div class="info-item full-width">
            <span class="info-label">收件人：</span>
            <span class="info-value">{{ getRecipientInfo() }}</span>
          </div>
        </div>
      </div>

      <!-- 主体内容区域 -->
      <el-row :gutter="20" class="main-content">
        <!-- 左侧：过程信息列表 -->
        <el-col :span="showDetailCard ? 14 : 24">
          <div class="process-section">
            <div class="section-header">
              <h3 class="section-title">
                <i class="el-icon-time"></i>
                过程信息
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-refresh"
                  @click="refreshProcessData"
                  style="margin-left: 10px"
                >
                  刷新
                </el-button>
              </h3>
              <div class="filter-controls">
                <el-select
                  v-model="selectedProcessType"
                  placeholder="全部"
                  size="small"
                  style="width: 120px"
                  @change="handleProcessTypeChange"
                >
                  <el-option label="全部" value=""></el-option>
                  <el-option
                    v-for="option in processTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  ></el-option>
                </el-select>
              </div>
            </div>

            <!-- 过程信息时间轴 -->
            <div class="process-timeline-container">
              <div
                v-if="filteredProcessData && filteredProcessData.length > 0"
                class="process-timeline"
              >
                <div
                  v-for="(item, index) in filteredProcessData"
                  :key="item.id || index"
                  class="process-item"
                  :class="{
                    active:
                      currentProcessItem && currentProcessItem.id === item.id,
                  }"
                  @click="showProcessDetail(item)"
                >
                  <div class="process-left">
                    <div class="process-number">
                      {{ filteredProcessData.length - index }}
                    </div>
                    <div
                      class="process-line"
                      v-if="index < filteredProcessData.length - 1"
                    ></div>
                  </div>
                  <div class="process-right">
                    <div class="process-header">
                      <span class="process-time">{{
                        formatDate(item.createTime)
                      }}</span>
                      <span class="process-unit">{{
                        item.eventReportingUnit ||
                        item.originalData?.courseName ||
                        "-"
                      }}</span>
                      <span class="process-source">{{
                        item.processTypeName ||
                        item.originalData?.courseTypeName ||
                        "系统接报"
                      }}</span>
                    </div>
                    <div class="process-content">
                      <div class="process-description">
                        {{
                          item.eventInfo ||
                          item.originalData?.courseInfo ||
                          "暂无描述"
                        }}
                      </div>
                    </div>
                    <div
                      class="process-expand-icon"
                      :class="{
                        expanded:
                          currentProcessItem &&
                          currentProcessItem.id === item.id,
                      }"
                    >
                      <i class="el-icon-arrow-right"></i>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 无数据状态 -->
              <div v-else class="no-data">
                <i class="el-icon-document"></i>
                <p>{{ loading ? "加载过程信息中..." : "暂无过程信息" }}</p>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 右侧：过程详情 -->
        <el-col :span="10" v-if="showDetailCard">
          <transition name="detail-slide" mode="out-in">
            <div class="detail-section" key="detail-card">
              <div class="section-header">
                <h3 class="section-title">
                  <i class="el-icon-document"></i>
                  过程详情
                </h3>
                <i class="el-icon-close close-btn" @click="closeDetailCard"></i>
              </div>

              <transition name="detail-content-fade" mode="out-in">
                <div
                  class="detail-content-area"
                  v-if="currentProcessItem && !detailLoading"
                  key="has-content"
                >
                  <!-- 责任部门 -->
                  <div class="detail-item" :style="{ animationDelay: '0.05s' }">
                    <div class="detail-label">责任部门：</div>
                    <div class="detail-value">
                      {{
                        currentProcessItem.responsibleDepartment ||
                        currentProcessItem.eventReportingUnit ||
                        currentProcessItem.originalData?.courseName ||
                        "-"
                      }}
                    </div>
                  </div>

                  <!-- 事件标题 -->
                  <div class="detail-item" :style="{ animationDelay: '0.08s' }">
                    <div class="detail-label">事件标题：</div>
                    <div class="detail-value">
                      {{
                        currentProcessItem.eventTitle ||
                        currentProcessItem.originalData?.infoTitle ||
                        "-"
                      }}
                    </div>
                  </div>

                  <!-- 事发时间 -->
                  <div class="detail-item" :style="{ animationDelay: '0.11s' }">
                    <div class="detail-label">事发时间：</div>
                    <div class="detail-value">
                      {{
                        formatDate(currentProcessItem.originalData?.infoTime) ||
                        "-"
                      }}
                    </div>
                  </div>

                  <!-- 事发地点 -->
                  <div class="detail-item" :style="{ animationDelay: '0.14s' }">
                    <div class="detail-label">事发地点：</div>
                    <div class="detail-value">
                      {{
                        currentProcessItem.originalData?.infoLocationDetail ||
                        "-"
                      }}
                    </div>
                  </div>

                  <!-- 事件详情 -->
                  <div
                    class="detail-item full-content"
                    :style="{ animationDelay: '0.17s' }"
                  >
                    <div class="detail-label">事件详情：</div>
                    <div class="detail-value detail-textarea">
                      {{
                        currentProcessItem.eventInfo ||
                        currentProcessItem.originalData?.courseInfo ||
                        "暂无详细信息"
                      }}
                    </div>
                  </div>

                  <!-- 上报信息 -->
                  <div class="detail-item" :style="{ animationDelay: '0.2s' }">
                    <div class="detail-label">上报时间：</div>
                    <div class="detail-value">
                      {{ formatDate(currentProcessItem.createTime) || "-" }}
                    </div>
                  </div>

                  <div class="detail-item" :style="{ animationDelay: '0.23s' }">
                    <div class="detail-label">上报单位：</div>
                    <div class="detail-value">
                      {{
                        currentProcessItem.eventReportingUnit ||
                        currentProcessItem.originalData?.courseName ||
                        "-"
                      }}
                    </div>
                  </div>

                  <div class="detail-item" :style="{ animationDelay: '0.26s' }">
                    <div class="detail-label">上报人：</div>
                    <div class="detail-value">
                      {{ currentProcessItem.eventReportingUser || "-" }}
                    </div>
                  </div>

                  <!-- 伤亡统计 -->
                  <div
                    class="casualty-summary"
                    :style="{ animationDelay: '0.29s' }"
                  >
                    <div class="casualty-item">
                      <span class="casualty-label">死亡：</span>
                      <span class="casualty-number death">{{
                        currentProcessItem.deathNum || 0
                      }}</span>
                    </div>
                    <div class="casualty-item">
                      <span class="casualty-label">失联：</span>
                      <span class="casualty-number missing">{{
                        currentProcessItem.missingNum || 0
                      }}</span>
                    </div>
                    <div class="casualty-item">
                      <span class="casualty-label">重伤：</span>
                      <span class="casualty-number severe">{{
                        currentProcessItem.severeInjuryNum || 0
                      }}</span>
                    </div>
                    <div class="casualty-item">
                      <span class="casualty-label">轻伤：</span>
                      <span class="casualty-number light">{{
                        currentProcessItem.lightInjuryNum || 0
                      }}</span>
                    </div>
                  </div>
                </div>

                <!-- 加载状态 -->
                <div
                  v-else-if="detailLoading"
                  class="detail-loading"
                  key="loading"
                >
                  <i class="el-icon-loading"></i>
                  <p>正在加载详情信息...</p>
                </div>

                <!-- 无选中状态 -->
                <div v-else class="no-selection" key="no-content">
                  <i class="el-icon-info"></i>
                  <p>请选择左侧过程信息查看详情</p>
                </div>
              </transition>
            </div>
          </transition>
        </el-col>
      </el-row>

      <!-- 底部操作区域 -->
      <div class="footer-actions">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSend">发送</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { receiveInformationApi, systemManagementApi } from "@/api";
import { mapState } from "vuex";

export default {
  name: "ReceiveInformationDetail",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailId: {
      type: [String, Number],
      default: "",
    },
    detailTitle: {
      type: String,
      default: "接报信息详情",
    },
  },
  data() {
    return {
      loading: false,
      detailLoading: false, // 过程详情加载状态
      dataLoaded: false, // 跟踪数据是否已加载
      showDetailCard: false, // 控制第三块区域显示
      detailData: {}, // 主详情数据
      processData: [], // 过程信息列表
      filteredProcessData: [], // 过滤后的过程信息列表
      currentProcessItem: null, // 当前选中的过程信息
      processTypeOptions: [], // 过程类型选项
      selectedProcessType: "", // 选中的过程类型
    };
  },
  watch: {
    dialogVisible: {
      handler(val) {
        if (val && this.detailId && !this.dataLoaded && !this.loading) {
          // 对话框打开且有detailId时加载数据
          this.loadDetailData();
        } else if (!val) {
          // 关闭对话框时重置状态
          this.resetDialogState();
        }
      },
      immediate: false,
    },
    detailId: {
      handler(val, oldVal) {
        // 只有当detailId变化且对话框可见、新ID有效、不同于旧ID时才加载数据
        if (this.dialogVisible && val && val !== oldVal && !this.loading) {
          this.resetDialogState();
          this.loadDetailData();
        }
      },
      immediate: false,
    },
  },
  computed: {
    ...mapState("user", ["userInfo"]),

    // 获取当前用户名
    currentUserName() {
      return this.userInfo?.name || "";
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },

    // 重置对话框状态
    resetDialogState() {
      this.dataLoaded = false;
      this.showDetailCard = false;
      this.currentProcessItem = null;
      this.detailData = {};
      this.processData = [];
      this.filteredProcessData = [];
      this.processTypeOptions = [];
      this.selectedProcessType = "";
      this.loading = false;
      this.detailLoading = false;
    },

    // 统一加载详情数据的方法
    async loadDetailData() {
      // 防止重复调用的多重检查
      if (!this.detailId || this.loading || this.dataLoaded) {
        return;
      }

      // 设置加载状态，防止重复调用
      this.loading = true;
      this.dataLoaded = false;

      // 重置详情卡片
      this.showDetailCard = false;
      this.currentProcessItem = null;

      try {
        // 并行加载，提升性能
        const [detailResult, processResult, typeOptionsResult] =
          await Promise.allSettled([
            this.fetchDetailData(),
            this.fetchProcessList(),
            this.loadProcessTypeOptions(),
          ]);

        // 检查各个请求的结果
        let hasError = false;
        const errors = [];

        if (detailResult.status === "rejected") {
          hasError = true;
          errors.push(
            `详情数据加载失败: ${
              detailResult.reason?.message || detailResult.reason
            }`
          );
          console.error("详情数据加载失败:", detailResult.reason);
        }

        if (processResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程信息加载失败: ${
              processResult.reason?.message || processResult.reason
            }`
          );
          console.error("过程信息加载失败:", processResult.reason);
        }

        if (typeOptionsResult.status === "rejected") {
          hasError = true;
          errors.push(
            `过程类型选项加载失败: ${
              typeOptionsResult.reason?.message || typeOptionsResult.reason
            }`
          );
          console.error("过程类型选项加载失败:", typeOptionsResult.reason);
        }

        // 如果有错误，显示错误信息，但不阻止页面显示
        if (hasError) {
          this.$message.warning(
            `部分数据加载失败，但可以继续使用: ${errors.join("; ")}`
          );
        }

        this.dataLoaded = true;
      } catch (error) {
        console.error("加载详情数据失败:", error);
        this.$message.error("加载详情数据失败: " + (error.message || error));
        this.dataLoaded = true; // 即使失败也标记为已加载，避免重复请求
      } finally {
        this.loading = false;
      }
    },

    // 获取收件人信息
    getRecipientInfo() {
      // 根据实际数据结构返回收件人信息
      const recipients = [];
      if (this.detailData.infoReportingUnit) {
        recipients.push(this.detailData.infoReportingUnit);
      }
      if (this.currentUserName) {
        recipients.push(`${this.currentUserName}(77)`);
      }
      return recipients.length > 0
        ? recipients.join(", ")
        : "系统接报(77), 市政府值班室(36/36), 市委市政府应急值班(10/10), 高新区应急值班(10/10), 高新区应急值班室(19/29)";
    },

    // 刷新过程数据
    async refreshProcessData() {
      if (!this.detailId) return;

      this.loading = true;
      try {
        await this.fetchProcessList();
        this.$message.success("过程信息刷新成功");
      } catch (error) {
        console.error("刷新过程信息失败:", error);
        this.$message.error("刷新过程信息失败");
      } finally {
        this.loading = false;
      }
    },

    // 发送操作
    handleSend() {
      this.$message.success("发送成功");
      this.handleClose();
    },

    // 显示过程信息详情
    async showProcessDetail(item) {
      if (!item || !item.id) return;

      // 如果点击的是同一个项目，则切换显示/隐藏状态
      if (this.currentProcessItem && this.currentProcessItem.id === item.id) {
        this.showDetailCard = !this.showDetailCard;
        if (!this.showDetailCard) {
          this.currentProcessItem = null;
        }
        return;
      }

      // 立即显示详情卡片并设置loading状态
      this.showDetailCard = true;
      this.detailLoading = true;
      this.currentProcessItem = null;

      try {
        // 简化请求参数，只传递必要的 otherId 和 otherType
        const requestParams = {
          otherId: item.originalData?.otherId || this.detailId,
          otherType: item.originalData?.otherType || "1",
        };

        // 调试日志：输出关键参数
        console.log("查询事件过程详情参数:", {
          otherId: requestParams.otherId,
          otherType: requestParams.otherType,
        });

        // 调用详情接口获取完整的过程信息
        const response = await receiveInformationApi.queryEventProcessInfo(
          requestParams
        );

        if (response && response.code === 0) {
          // 使用接口返回的详细数据，根据实际接口响应结构进行映射
          this.currentProcessItem = {
            ...item,
            // 基本信息
            id: response.data?.id || item.id,
            eventId: response.data?.reportInfoId || item.eventId,
            eventTitle: response.data?.infoTitle || item.eventTitle,
            eventInfo: response.data?.eventInfo || item.eventInfo,
            createTime: response.data?.createTime || item.createTime,
            updateTime: response.data?.updateTime || item.updateTime,

            // 地理位置信息
            infoDistrict: response.data?.infoDistrict,
            infoTownshipStreet: response.data?.infoTownshipStreet,
            infoLocationDetail: response.data?.infoLocationDetail,
            infoLongitude: response.data?.infoLongitude,
            infoLatitude: response.data?.infoLatitude,

            // 事件分类信息
            infoType: response.data?.infoType,
            infoChildType: response.data?.infoChildType,
            infoThirdType: response.data?.infoThirdType,

            // 伤亡统计
            deathNum: response.data?.deathNum || 0,
            missingNum: response.data?.missingNum || 0,
            severeInjuryNum: response.data?.severeInjuryNum || 0,
            lightInjuryNum: response.data?.lightInjuryNum || 0,

            // 上报信息
            infoReportingMethod: response.data?.infoReportingMethod,
            infoReportingUnit:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",
            infoReportingUser: response.data?.infoReportingUser,
            eventReportingUnit:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",

            // 责任部门（使用上报单位作为责任部门）
            responsibleDepartment:
              response.data?.infoReportingUnit ||
              item.eventReportingUnit ||
              "-",

            // 保留原始数据
            originalData: response.data,
          };
        } else {
          // 如果接口调用失败，使用列表数据作为备用
          console.warn("获取过程详情失败，使用列表数据:", response?.message);
          this.currentProcessItem = {
            ...item,
            eventReportingUnit:
              item.eventReportingUnit ||
              this.detailData.infoReportingUnit ||
              "-",
          };
        }
      } catch (error) {
        console.error("获取过程详情出错:", error);
        // 出错时使用列表数据作为备用
        this.currentProcessItem = {
          ...item,
          eventReportingUnit:
            item.eventReportingUnit || this.detailData.infoReportingUnit || "-",
        };
      } finally {
        // 无论成功还是失败，都要关闭loading状态
        this.detailLoading = false;
      }
    },

    // 关闭详情卡片
    closeDetailCard() {
      this.showDetailCard = false;
      this.currentProcessItem = null;
    },

    // 获取主要详情数据
    async fetchDetailData() {
      if (!this.detailId) return;

      try {
        const response = await receiveInformationApi.queryReportInfo({
          id: this.detailId,
        });

        if (response && response.code === 0) {
          this.detailData = response.data || {};
        } else {
          this.$message.error(response?.message || "获取详情失败");
          throw new Error(response?.message || "获取详情失败");
        }
      } catch (error) {
        console.error("获取详情出错:", error);
        this.$message.error("获取详情失败");
        throw error;
      }
    },

    // 获取过程信息列表 - 使用新的API接口
    async fetchProcessList() {
      if (!this.detailId) return;

      try {
        // 构建请求参数，根据接口文档使用正确的参数名
        const params = {
          id: this.detailId, // 事件ID
        };

        // 如果有选中的过程类型，添加过滤条件
        if (this.selectedProcessType) {
          params.processType = this.selectedProcessType;
        }

        const response = await receiveInformationApi.queryEventProcessInfoList(
          params
        );

        if (response && response.code === 0) {
          // 接口返回的data直接是数组
          let processData = [];
          if (Array.isArray(response.data)) {
            // 调试日志：输出原始列表数据
            console.log("过程信息列表原始数据:", response.data);

            // 根据实际接口响应数据结构进行字段映射
            processData = response.data.map((item) => {
              const mappedItem = {
                id: item.id,
                eventId: this.detailId, // 使用当前事件ID，而不是不存在的 otherId
                reportId: item.reportId, // 报告ID
                createTime: item.courseTime || item.createTime, // 过程时间
                processType: item.courseType, // 过程类型
                processTypeName: item.courseTypeName, // 过程类型名称
                eventInfo: item.courseInfo, // 过程信息内容
                eventTitle: item.courseName, // 过程标题
                eventReportingUnit: item.courseName || "-", // 上报单位（使用courseName）
                eventReportingUser: "-", // 上报用户（接口未返回）
                responsibleDepartment: item.courseName || "-", // 责任部门
                updateTime: item.updateTime,
                // 保留原始数据，完全基于接口返回的字段
                originalData: item,
              };

              // 调试日志：输出接口实际返回的数据结构
              console.log(`过程项 ${item.id} 接口原始数据:`, item);
              console.log(`过程项 ${item.id} 可用字段:`, Object.keys(item));
              console.log(`过程项 ${item.id} 关键字段检查:`, {
                id: item.id,
                courseInfo: item.courseInfo,
                courseName: item.courseName,
                courseTime: item.courseTime,
                courseType: item.courseType,
                courseTypeName: item.courseTypeName,
                createTime: item.createTime,
                reportId: item.reportId,
                updateTime: item.updateTime,
                otherId: item.otherId,
                otherType: item.otherType,
              });

              return mappedItem;
            });
          } else if (response.data && Array.isArray(response.data.items)) {
            processData = response.data.items;
          } else if (response.data && Array.isArray(response.data.records)) {
            processData = response.data.records;
          }

          this.processData = processData || [];

          // 初始化过滤数据
          this.filterProcessData();

          // 强制更新视图
          this.$nextTick(() => {
            this.$forceUpdate();
          });
        } else {
          this.$message.error(response?.message || "获取过程信息失败");
          throw new Error(response?.message || "获取过程信息失败");
        }
      } catch (error) {
        console.error("获取过程信息出错:", error);
        this.$message.error("获取过程信息列表失败");
        throw error;
      }
    },

    // 加载过程类型选项
    async loadProcessTypeOptions() {
      try {
        const response = await systemManagementApi.queryItemListById({
          systemDictionaryId: "202507051549",
        });

        if (response && response.code === 0) {
          this.processTypeOptions = (response.data || []).map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        } else {
          throw new Error(response?.message || "获取过程类型选项失败");
        }
      } catch (error) {
        console.error("获取过程类型选项失败:", error);
        throw error;
      }
    },

    // 过程类型变化处理
    async handleProcessTypeChange() {
      try {
        this.loading = true;
        await this.fetchProcessList();
      } catch (error) {
        console.error("过程类型过滤失败:", error);
        this.filterProcessData();
      } finally {
        this.loading = false;
      }
    },

    // 过滤过程信息数据（客户端过滤）
    filterProcessData() {
      if (!this.selectedProcessType) {
        this.filteredProcessData = [...this.processData];
      } else {
        this.filteredProcessData = this.processData.filter(
          (item) => item.processType === this.selectedProcessType
        );
      }
    },

    // 根据过程类型ID获取名称
    getProcessTypeName(processTypeId) {
      if (!processTypeId) return "";
      const option = this.processTypeOptions.find(
        (opt) => opt.value === processTypeId
      );
      return option ? option.label : processTypeId;
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "";
      try {
        const date = new Date(dateStr);
        return date.toLocaleString();
      } catch (e) {
        return dateStr;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.receive-info-detail-dialog {
  .el-dialog__body {
    padding: 20px;
  }
}

.detail-content {
  min-height: 600px;
}

// 顶部基本信息区域
.header-info {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  min-width: 0; // 确保flex容器能够收缩

  .info-items-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 12px;
    align-items: center;
    overflow-x: auto;
    padding: 4px 0;

    .info-item {
      display: flex;
      align-items: center;
      flex: 0 0 auto;
      min-width: 80px;
      white-space: nowrap;
      background: #fff;
      border: 1px solid #e1e8ed;
      border-radius: 6px;
      padding: 8px 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.2s ease;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        transform: translateY(-1px);
      }

      &.full-width {
        flex: 1 1 auto;
        min-width: 200px;
        margin-top: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
        border-color: #d1d9e0;
      }

      .info-label {
        font-size: 12px;
        color: #6b7280;
        margin-right: 8px;
        white-space: nowrap;
        flex-shrink: 0;
        font-weight: 500;
        background: #f3f4f6;
        padding: 2px 6px;
        border-radius: 4px;
        border: 1px solid #e5e7eb;
      }

      .info-value {
        font-size: 13px;
        color: #1f2937;
        font-weight: 600;
        line-height: 1.4;
        flex: 1;
        white-space: nowrap;

        &.status-processing {
          color: #409eff;
          background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
          border: 1px solid #90caf9;
          border-radius: 6px;
          padding: 4px 8px;
          font-weight: 600;

          i {
            margin-right: 6px;
            font-size: 14px;
          }
        }
      }

      &.full-width {
        .info-label {
          background: #e8f4fd;
          border-color: #b3d9f2;
          color: #1565c0;
        }

        .info-value {
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: calc(100% - 80px);
        }
      }
    }
  }
}

// 主体内容区域
.main-content {
  min-height: 500px;
}

// 过程信息区域
.process-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  height: 500px;
  display: flex;
  flex-direction: column;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
      display: flex;
      align-items: center;

      i {
        margin-right: 8px;
        color: #409eff;
      }
    }

    .filter-controls {
      display: flex;
      align-items: center;
    }
  }

  .process-timeline-container {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;
  }
}

// 过程信息时间轴样式
.process-timeline {
  position: relative;

  // 主时间轴线
  &::before {
    content: "";
    position: absolute;
    left: -10px;
    height: 145px;
    top: 50px;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #409eff 0%, #176ec5 100%);
    z-index: 1;
  }

  .process-item {
    display: flex;
    margin-bottom: 20px;
    cursor: pointer;
    padding: 16px 20px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

    // 卡片连接线
    &::before {
      content: "";
      position: absolute;
      left: -8px;
      top: 50%;
      width: 16px;
      height: 2px;
      background: #e9ecef;
      transform: translateY(-50%);
      z-index: 2;
      transition: all 0.3s ease;
    }

    &:hover {
      background: linear-gradient(135deg, #f8fafc 0%, #e3f2fd 100%);
      border-color: #409eff;
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
      transform: translateX(8px);

      &::before {
        background: #409eff;
        width: 20px;
      }
    }

    &.active {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-color: #409eff;
      box-shadow: 0 12px 30px rgba(64, 158, 255, 0.25);
      transform: translateX(12px);

      &::before {
        background: #409eff;
        width: 24px;
      }
    }

    &:last-child {
      margin-bottom: 0;
    }

    .process-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 20px;
      position: relative;
      z-index: 3;

      .process-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
        color: #fff;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: relative;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        border: 3px solid #fff;
        transition: all 0.3s ease;

        // 数字圆圈的光晕效果
        &::before {
          content: "";
          position: absolute;
          top: -3px;
          left: -3px;
          right: -3px;
          bottom: -3px;
          border-radius: 50%;
          background: linear-gradient(
            135deg,
            rgba(64, 158, 255, 0.2) 0%,
            rgba(25, 118, 210, 0.2) 100%
          );
          z-index: -1;
          opacity: 0;
          transition: all 0.3s ease;
        }
      }

      // 移除原来的连接线，因为现在使用主时间轴线
      .process-line {
        display: none;
      }
    }

    // 悬停和激活状态的数字样式
    &:hover .process-left .process-number {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.2);
      }
    }

    &.active .process-left .process-number {
      background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
      transform: scale(1.15);
      box-shadow: 0 8px 25px rgba(255, 107, 53, 0.4);

      &::before {
        opacity: 1;
        transform: scale(1.3);
        background: linear-gradient(
          135deg,
          rgba(255, 107, 53, 0.3) 0%,
          rgba(247, 147, 30, 0.3) 100%
        );
      }
    }

    .process-right {
      flex: 1;
      position: relative;
      padding-left: 4px;

      .process-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
        flex-wrap: wrap;
        gap: 8px;

        .process-time {
          font-size: 15px;
          color: #1a1a1a;
          font-weight: 700;
          background: linear-gradient(135deg, #409eff 0%, #1976d2 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          flex-shrink: 0;
        }

        .process-unit {
          font-size: 13px;
          color: #409eff;
          font-weight: 600;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 10px;
          border-radius: 16px;
          border: 1px solid rgba(64, 158, 255, 0.2);
          flex-shrink: 0;
        }

        .process-source {
          font-size: 11px;
          color: #666;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 4px 10px;
          border-radius: 14px;
          border: 1px solid #e9ecef;
          font-weight: 500;
          flex-shrink: 0;
        }
      }

      .process-content {
        .process-description {
          font-size: 14px;
          color: #4a5568;
          line-height: 1.6;
          margin-bottom: 0;
          padding: 8px 0;
          border-left: 3px solid transparent;
          padding-left: 12px;
          transition: all 0.3s ease;
          background: rgba(248, 250, 252, 0.5);
          border-radius: 0 8px 8px 0;
          margin-left: -12px;
          padding-left: 16px;
        }
      }

      .process-expand-icon {
        position: absolute;
        right: 16px;
        top: 75%;
        transform: translateY(-50%);
        color: #cbd5e0;
        font-size: 18px;
        transition: all 0.3s ease;
        cursor: pointer;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid #e2e8f0;

        i {
          transition: transform 0.3s ease;
        }

        &.expanded {
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          border-color: #409eff;

          i {
            transform: rotate(90deg);
          }
        }

        &:hover {
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          border-color: #409eff;
          transform: translateY(-50%) scale(1.1);
        }
      }
    }

    // 悬停状态的内容样式
    &:hover .process-right .process-content .process-description {
      border-left-color: #409eff;
      background: rgba(64, 158, 255, 0.05);
    }

    // 激活状态的内容样式
    &.active .process-right .process-content .process-description {
      border-left-color: #ff6b35;
      background: rgba(255, 107, 53, 0.05);
      color: #2d3748;
      font-weight: 500;
    }
  }

  // 步骤轴进入动画
  .process-item {
    animation: slideInFromLeft 0.6s ease-out both;

    &:nth-child(1) {
      animation-delay: 0.1s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.3s;
    }
    &:nth-child(4) {
      animation-delay: 0.4s;
    }
    &:nth-child(5) {
      animation-delay: 0.5s;
    }
    &:nth-child(n + 6) {
      animation-delay: 0.6s;
    }
  }
}

// 步骤轴动画关键帧
@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// 数字脉动动画
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

// 激活状态的数字添加脉动效果
.process-timeline .process-item.active .process-left .process-number {
  animation: pulse 2s infinite;
}

// 详情区域样式
.detail-section {
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  height: 500px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;

  &:hover {
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.1);
    border-color: #409eff;
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
      display: flex;
      align-items: center;
      transition: color 0.3s ease;

      i {
        margin-right: 8px;
        color: #409eff;
        transition: transform 0.3s ease;
      }
    }

    .close-btn {
      cursor: pointer;
      color: #999;
      font-size: 18px;
      transition: all 0.3s ease;
      padding: 4px;
      border-radius: 50%;

      &:hover {
        color: #f56c6c;
        background: rgba(245, 108, 108, 0.1);
        transform: scale(1.1);
      }
    }
  }

  .detail-content-area {
    flex: 1;
    overflow-y: auto;
    padding: 16px 20px;

    .detail-item {
      margin-bottom: 16px;
      padding: 8px 0;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.02);
        padding-left: 8px;
        margin-left: -8px;
        margin-right: -8px;
      }

      &.full-content {
        margin-bottom: 20px;
      }

      .detail-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 6px;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      .detail-value {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
        transition: all 0.3s ease;

        &.detail-textarea {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 4px;
          padding: 12px;
          min-height: 80px;
          line-height: 1.6;
          transition: all 0.3s ease;

          &:hover {
            background: #f0f7ff;
            border-color: #409eff;
          }
        }
      }
    }

    .casualty-summary {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      margin-top: 16px;
      padding: 12px;
      background: linear-gradient(135deg, #f8f9fa 0%, #f0f7ff 100%);
      border-radius: 6px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
      }

      .casualty-item {
        display: flex;
        align-items: center;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.05);
        }

        .casualty-label {
          font-size: 13px;
          color: #666;
          margin-right: 4px;
        }

        .casualty-number {
          font-size: 16px;
          font-weight: 600;
          padding: 4px 10px;
          border-radius: 6px;
          transition: all 0.3s ease;
          cursor: default;

          &:hover {
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          &.death {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.missing {
            color: #e6a23c;
            background: linear-gradient(135deg, #fdf6ec 0%, #fed7aa 100%);
            border: 1px solid #fde68a;
          }

          &.severe {
            color: #f56c6c;
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            border: 1px solid #fecaca;
          }

          &.light {
            color: #67c23a;
            background: linear-gradient(135deg, #f0f9eb 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
          }
        }
      }
    }
  }

  .no-selection,
  .detail-loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    transition: all 0.3s ease;

    i {
      font-size: 48px;
      margin-bottom: 12px;
      color: #ddd;
      transition: all 0.3s ease;
    }

    p {
      font-size: 14px;
      margin: 0;
      transition: color 0.3s ease;
    }
  }

  .no-selection {
    i {
      animation: pulse 2s infinite;
    }

    &:hover {
      color: #666;

      i {
        color: #409eff;
        transform: scale(1.1);
      }
    }
  }

  .detail-loading {
    color: #409eff;

    i {
      color: #409eff;
      animation: rotate 1s linear infinite;
    }

    p {
      color: #666;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 15px;
  }

  p {
    font-size: 14px;
  }
}

// 底部操作区域
.footer-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 0;
  margin-top: 20px;
  border-top: 1px solid #e9ecef;
  gap: 12px;
}

// 详情卡片滑入/滑出动画
.detail-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: relative;
  z-index: 1;
}

.detail-slide-leave-active {
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 0;
}

.detail-slide-enter-from {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

.detail-slide-leave-to {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
}

.detail-slide-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
}

.detail-slide-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

// 详情内容淡入/淡出动画
.detail-content-fade-enter-active {
  transition: all 0.25s ease-out;
}

.detail-content-fade-leave-active {
  transition: all 0.2s ease-in;
  animation: fadeOutDown 0.2s ease-in;
}

.detail-content-fade-enter-from {
  opacity: 0;
  transform: translateY(15px);
}

.detail-content-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.detail-content-fade-enter-to {
  opacity: 1;
  transform: translateY(0);
}

.detail-content-fade-leave-from {
  opacity: 1;
  transform: translateY(0);
}

// 关键帧动画已移除，使用CSS transition替代

// 内容淡出动画
@keyframes fadeOutDown {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(15px);
  }
}

// 详情项动画
.detail-content-area .detail-item,
.detail-content-area .casualty-summary {
  animation: fadeInUp 0.3s ease-out both;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式处理
@media (max-width: 1400px) {
  .header-info .info-items-container {
    gap: 8px;

    .info-item {
      min-width: 60px;
      padding: 6px 10px;

      .info-label {
        font-size: 11px;
        padding: 1px 4px;
        margin-right: 6px;
      }

      .info-value {
        font-size: 12px;
      }

      &.full-width .info-value {
        max-width: calc(100% - 70px);
      }
    }
  }
}

@media (max-width: 1200px) {
  .header-info .info-items-container {
    flex-wrap: wrap;

    .info-item.full-width {
      flex: 1 1 100%;
      margin-top: 6px;
      white-space: normal;

      .info-value {
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
        max-width: none;
      }
    }
  }
}
</style>
