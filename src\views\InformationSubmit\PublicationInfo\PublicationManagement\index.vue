<template>
  <div class="publication-info-container">
    <!-- 刊物信息管理 - Page -->
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <el-tab-pane label="普刊" name="general">
        <portal-table
          ref="generalTableRef"
          :showAddButton="false"
          :columns="generalColumns"
          :pagination="generalPagination"
          :search-items="generalSearchItems"
          :table-data="generalTableData"
          :loading="generalLoading"
          row-key="id"
          @search="handleGeneralSearch"
          @size-change="handleGeneralSizeChange"
          @current-change="handleGeneralCurrentChange"
          @handle-selection-change="handleGeneralSelectionChange"
        />
      </el-tab-pane>

      <el-tab-pane label="要情" name="important">
        <portal-table
          ref="importantTableRef"
          :showAddButton="false"
          :columns="importantColumns"
          :pagination="importantPagination"
          :search-items="importantSearchItems"
          :table-data="importantTableData"
          :loading="importantLoading"
          row-key="id"
          @search="handleImportantSearch"
          @size-change="handleImportantSizeChange"
          @current-change="handleImportantCurrentChange"
          @handle-selection-change="handleImportantSelectionChange"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";

export default {
  name: "PublicationInfo",
  components: {
    PortalTable,
  },
  data() {
    return {
      activeTab: "general", // 默认选中普刊标签页

      // 普刊相关数据
      generalLoading: false,
      generalTableData: [],
      generalCurrentSearchParams: {},
      generalPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 要情相关数据
      importantLoading: false,
      importantTableData: [],
      importantCurrentSearchParams: {},
      importantPagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 普刊搜索表单配置
      generalSearchItems: [
        {
          type: "input",
          prop: "issueNumber",
          label: "期号",
          width: 200,
          placeholder: "请输入期号",
        },
        {
          type: "datePicker",
          prop: "submitTime",
          label: "提交时间",
          placeholder: "请选择提交时间",
          width: 200,
        },
        {
          type: "select",
          prop: "status",
          label: "状态",
          placeholder: "请选择状态",
          options: [
            { label: "全部", value: "" },
            { label: "暂存", value: "draft" },
            { label: "已提交", value: "submitted" },
            { label: "已批示", value: "approved" },
          ],
          width: 200,
        },
      ],

      // 要情搜索表单配置
      importantSearchItems: [
        {
          type: "input",
          prop: "issueNumber",
          label: "期号",
          width: 200,
          placeholder: "请输入期号",
        },
        {
          type: "datePicker",
          prop: "submitTime",
          label: "提交时间",
          placeholder: "请选择提交时间",
          width: 200,
        },
        {
          type: "select",
          prop: "status",
          label: "状态",
          placeholder: "请选择状态",
          options: [
            { label: "全部", value: "" },
            { label: "暂存", value: "draft" },
            { label: "已提交", value: "submitted" },
            { label: "已批示", value: "approved" },
          ],
          width: 200,
        },
      ],

      // 普刊列表列配置
      generalColumns: [
        {
          prop: "issueNumber",
          label: "期号",
          text: true,
        },
        {
          prop: "submitTime",
          label: "提交时间",
          text: true,
        },
        {
          prop: "approvalTime",
          label: "批示时间",
          text: true,
        },
        {
          prop: "statusText",
          label: "状态",
          text: true,
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "预览",
              permission: "general:preview",
              buttonClick: this.handleGeneralPreview,
              isShow: () => true,
            },
            {
              label: "编辑",
              permission: "general:edit",
              buttonClick: this.handleGeneralEdit,
              isShow: () => true,
            },
            {
              label: "删除",
              permission: "general:delete",
              buttonClick: this.handleGeneralDelete,
              isShow: () => true,
            },
          ],
        },
      ],

      // 要情列表列配置
      importantColumns: [
        {
          prop: "issueNumber",
          label: "期号",
          text: true,
        },
        {
          prop: "submitTime",
          label: "提交时间",
          text: true,
        },
        {
          prop: "approvalTime",
          label: "批示时间",
          text: true,
        },
        {
          prop: "statusText",
          label: "状态",
          text: true,
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "预览",
              permission: "important:preview",
              buttonClick: this.handleImportantPreview,
              isShow: () => true,
            },
            {
              label: "编辑",
              permission: "important:edit",
              buttonClick: this.handleImportantEdit,
              isShow: () => true,
            },
            {
              label: "批示",
              permission: "important:approve",
              buttonClick: this.handleImportantApprove,
              isShow: () => true,
            },
          ],
        },
      ],
    };
  },
  mounted() {
    this.fetchGeneralData();
  },
  methods: {
    // Tab切换处理
    handleTabClick(tab) {
      if (tab.name === "general") {
        this.fetchGeneralData();
      } else if (tab.name === "important") {
        this.fetchImportantData();
      }
    },

    // 普刊相关方法
    async fetchGeneralData(searchParams = {}) {
      this.generalLoading = true;
      try {
        // 模拟API调用
        const mockData = this.generateMockGeneralData();
        this.generalTableData = mockData.items;
        this.generalPagination.total = mockData.total;
      } catch (error) {
        this.$message.error("普刊数据加载失败");
        this.generalTableData = [];
        this.generalPagination.total = 0;
      } finally {
        this.generalLoading = false;
      }
    },

    handleGeneralSearch(searchData) {
      this.generalCurrentSearchParams = searchData;
      this.generalPagination.currentPage = 1;
      this.fetchGeneralData(searchData);
    },

    handleGeneralSizeChange(size) {
      this.generalPagination.pageSize = size;
      this.fetchGeneralData(this.generalCurrentSearchParams);
    },

    handleGeneralCurrentChange(page) {
      this.generalPagination.currentPage = page;
      this.fetchGeneralData(this.generalCurrentSearchParams);
    },

    handleGeneralSelectionChange(selection) {
      // 处理普刊选择变化
    },

    handleGeneralPreview(row) {
      this.$message.info(`预览普刊：${row.issueNumber}`);
    },

    handleGeneralEdit(row) {
      // 跳转到编辑页面
      this.$router.push({
        path: `/informationSubmit/publicationInfo/publicationManagement/edit`,
        query: {
          id: row.id,
          type: "general",
        },
      });
    },

    handleGeneralDelete(row) {
      this.$confirm(`确定要删除期号为"${row.issueNumber}"的普刊吗？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("删除成功");
        this.fetchGeneralData(this.generalCurrentSearchParams);
      });
    },

    // 要情相关方法
    async fetchImportantData(searchParams = {}) {
      this.importantLoading = true;
      try {
        // 模拟API调用
        const mockData = this.generateMockImportantData();
        this.importantTableData = mockData.items;
        this.importantPagination.total = mockData.total;
      } catch (error) {
        this.$message.error("要情数据加载失败");
        this.importantTableData = [];
        this.importantPagination.total = 0;
      } finally {
        this.importantLoading = false;
      }
    },

    handleImportantSearch(searchData) {
      this.importantCurrentSearchParams = searchData;
      this.importantPagination.currentPage = 1;
      this.fetchImportantData(searchData);
    },

    handleImportantSizeChange(size) {
      this.importantPagination.pageSize = size;
      this.fetchImportantData(this.importantCurrentSearchParams);
    },

    handleImportantCurrentChange(page) {
      this.importantPagination.currentPage = page;
      this.fetchImportantData(this.importantCurrentSearchParams);
    },

    handleImportantSelectionChange(selection) {
      // 处理要情选择变化
    },

    handleImportantPreview(row) {
      this.$message.info(`预览要情：${row.issueNumber}`);
    },

    handleImportantEdit(row) {
      // 跳转到编辑页面
      this.$router.push({
        path: `/informationSubmit/publicationInfo/publicationManagement/edit`,
        query: {
          id: row.id,
          type: "important",
        },
      });
    },

    handleImportantApprove(row) {
      this.$message.info(`批示要情：${row.issueNumber}`);
    },

    // 生成模拟数据
    generateMockGeneralData() {
      const items = [];
      for (let i = 1; i <= 10; i++) {
        const statusMap = {
          draft: "暂存",
          submitted: "已提交",
          approved: "已批示",
        };
        const status = ["draft", "submitted", "approved"][
          Math.floor(Math.random() * 3)
        ];

        items.push({
          id: i,
          issueNumber: `普刊第${173 - i + 1}期`,
          submitTime: status !== "draft" ? "2025-06-22 12:32" : "-",
          approvalTime: status === "approved" ? "2025-06-22 12:32" : "-",
          status: status,
          statusText: statusMap[status],
        });
      }
      return { items, total: 25 };
    },

    generateMockImportantData() {
      const items = [];
      for (let i = 1; i <= 8; i++) {
        const statusMap = {
          draft: "暂存",
          submitted: "已提交",
          approved: "已批示",
        };
        const status = ["draft", "submitted", "approved"][
          Math.floor(Math.random() * 3)
        ];

        items.push({
          id: i,
          issueNumber: `要情第${169 - i + 1}期`,
          submitTime: status !== "draft" ? "2025-06-22 12:32" : "-",
          approvalTime: status === "approved" ? "2025-06-22 12:32" : "-",
          status: status,
          statusText: statusMap[status],
        });
      }
      return { items, total: 18 };
    },
  },
};
</script>

<style scoped lang="scss">
.publication-info-container {
  padding: 20px;
}
</style>
