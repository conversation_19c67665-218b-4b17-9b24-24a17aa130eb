<!-- 会议材料---HistoryMeeting -->
<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :show-footer="false"
      @cancel="handleCancel"
    >
      <div class="preview-div">
        <div class="preview-list-content" v-for="item in fileList">
          <div class="file-icon-name">
            <img :src="getFileIcon(item.url)" class="file-icon" alt="" />
            <span class="file-icon-name">{{ item.name }}</span>
          </div>
          <div class="button-operate">
            <el-button v-if="styleType === 1" type="primary" size="mini" @click="handleDownload(item)">
              下载
            </el-button>
            <el-button
                v-if="styleType === 2"
              type="primary"
              size="mini"
              @click="handlePreview(item.url)"
            >
              预览
            </el-button>
          </div>
        </div>
      </div>
    </general-dialog>

    <el-dialog
        title="预览"
        :custom-class="'preview-dialog'"
        :visible.sync="showIframe"
        v-if="showIframe"
        width="90%"
        :before-close="handleCloseIframe"
    >
      <iframe
          :src="preFileUrl"
          frameborder="0"
          width="100%"
          height="100%"
      ></iframe>
    </el-dialog>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { meetingManagementApi } from "@/api";

import { conversionDateNotSecond, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils";

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "meetingName",
          label: "会议名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingTime",
          label: "会议日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260",
        },
        {
          prop: "meetingDescribe",
          label: "会议说明",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "inspectionType",
          label: "会议类别",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "meetingName", label: "会议名称", text: true },
        { prop: "meetingDate", label: "会议日期", text: true },
        { prop: "files", label: "会议附件", text: true },
        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "下载",
              permission: "dutyCheck:download",
              buttonClick: (row) => this.handleEdit(row,1),
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "预览",
              permission: "dutyCheck:download",
              buttonClick: (row) => this.handleEdit(row,2),
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "分享",
              permission: "dutyCheck:signUp",
              buttonClick: this.handleSignUp,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "删除",
              permission: "dutyCheck:delete",
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],

      styleType: 1, //1：下载，2：预览
      inspectionTypeList: [],
      inspectionResultList: [],
      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "760px",
      generalDialogTitle: "会议材料",

      form: {
        inspectionTime: "",
        inspectionType: "",
        inspectionUnit: "",
        inspectionJob: "",
        inspectionUser: "",
        inspectionResult: "",
        fileList: [],
      },
      rules: {
        inspectionTime: [
          { required: true, message: "检查时间不能为空", trigger: "blur" },
        ],
        inspectionType: [
          { required: true, message: "检查方式不能为空", trigger: "blur" },
        ],
        inspectionUnit: [
          { required: true, message: "被检查单位不能为空", trigger: "blur" },
        ],
        inspectionJob: [
          { required: true, message: "被检查岗位不能为空", trigger: "blur" },
        ],
        inspectionUser: [
          { required: true, message: "被检查人员不能为空", trigger: "blur" },
        ],
        // inspectionResult: [
        //   {required: true, message: '检查结果不能为空', trigger: 'blur'}
        // ]
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
  },
  methods: {
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          this.searchItems[3].options = this.meetingTypeList.map((item) => ({
            label: item.itemName,
            value: item.id,
          }));
        }
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      if (row.fileList && row.fileList.length > 0) {
        row.fileList.forEach((item) => {
          this.fileList.push({
            name: item.fileName,
            url: this.fileBaseUrl + item.fileUrl,
            id: item.id,
          });
        });
      } else {
        row.fileList = [];
      }
      this.form = {
        ...row,
      };
    },

    //编辑
    handleEdit(row, type) {
      this.getRowDataInfo(row);
      if (this.fileList.length > 1 ){
        this.styleType = type;
        this.dialogVisible = true;
      }else{
        if (type === 1) {
          this.handleDownload(this.fileList[0])
        }else if (type === 2) {
          this.handlePreview(this.fileList[0].url)
        }
      }


    },

    //删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该条数据, 是否继续?", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        let ids = row.fileList.map((item) => {
          return item.id;
        });
        const res = await meetingManagementApi.deleteMeetingFile({ ids: ids });
        const { code, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("删除成功");
        await this.getTableDataList();
      });
    },

    handleDownload(row) {
      console.log(row)
      const loading = this.$loading({
        lock: true,
        text: "正在下载...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {

        if (!row.url) {
          this.$message.warning("文件链接无效");
          loading.close();
          return;
        }

        fetch(row.url)
          .then((response) => {
            if (!response.ok)
              throw new Error(`HTTP error! status: ${response.status}`);
            return response.blob();
          })
          .then((blob) => {
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement("a");
            link.href = url;
            link.download = row.name; // 使用服务器返回的文件名
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
          })
          .catch((error) => {
            console.error("文件下载失败:", error);
            this.$message.error("文件下载失败，请检查文件是否存在");
          })
          .finally(() => {
            loading.close();
          });
      } catch (error) {
        console.error("下载异常:", error);
        loading.close();
        this.$message.error("下载过程中发生错误");
      }
    },

    handleSignUp() {
      this.$confirm("确定分享此次会议, 是否继续?", "分享", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.$message.success("分享成功");
      });
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        type: 2, //类型 1:会议纪要 2:会议材料
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams,
      };
      const res = await meetingManagementApi.queryMeetingFilePage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error);
      console.log("查询列表", data);
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1;
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0]);
        row.endTime = conversionDateNotSecond(row.inspectionTime[1]);
        delete row.inspectionTime;
      }
      this.searchParams = row;
      this.getTableDataList();
    },

    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },

    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
    },

    getFileIcon(url) {
      const extension = url?.split(".").pop().toLowerCase() || "";
      const iconMap = {
        xlsx: "excel",
        xls: "excel",
        doc: "word",
        docx: "word",
        pdf: "pdf",
      };
      return require(`@/assets/images/icons/${
        iconMap[extension] || "unknown"
      }.svg`);
    },

    handlePreview(fileUrl) {
      this.dialogVisible = false;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl =
          "http://" +
          getIPAddress() +
          ":8012/onlinePreview?url=" +
          encodeURIComponent(btoa(fileUrl));
      }
      //window.open(fileUrl, "_blank");
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },

    handleCloseIframe(){
      this.showIframe = false;
    }
  },
};
</script>

<style lang="scss" scoped>
.preview-div {
  padding: 30px 60px 5px 60px;

  .preview-list-content {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-bottom: 20px;

    .file-icon-name {
      display: flex;
      align-items: center;
      .file-icon {
        width: 30px;
        height: 30px;
        margin-right: 10px;
      }

      .file-icon-name {
        color: #0a1629;
        font-size: 18px;
        font-weight: 700;
        width: 500px;
      }
    }

    .button-operate {
      .el-button {
        width: 100px;
        height: 40px;
        font-size: 16px;
        margin-right: 10px;
        margin-left: 10px
      }
    }
  }
}

::v-deep{
  .preview-dialog{
    height: 90%;
    margin-top: 35px !important;

    .el-dialog__body{
      height: 93%;
    }
  }
}
</style>
