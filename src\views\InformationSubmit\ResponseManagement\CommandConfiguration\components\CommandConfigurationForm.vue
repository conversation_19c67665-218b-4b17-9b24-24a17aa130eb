<template>
  <div class="command-configuration-form">
    <!-- 指令配置表单 - Component -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      label-position="top"
      class="command-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="事故类型" prop="accidentType">
            <el-input
              v-model="formData.accidentType"
              placeholder="请输入事故类型"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="事故小类" prop="accidentSubType">
            <el-input
              v-model="formData.accidentSubType"
              placeholder="请输入事故小类"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="响应指令" prop="responseCommand">
            <el-input
              v-model="formData.responseCommand"
              type="textarea"
              :rows="3"
              placeholder="请输入响应指令"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="机构/单位/名称" prop="orgId">
            <el-select
              v-model="formData.orgId"
              placeholder="请选择机构/单位/名称"
              clearable
              filterable
              style="width: 100%"
              @change="handleOrgChange"
            >
              <el-option
                v-for="item in orgOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指挥部/现场指挥部" prop="commandCenter">
            <el-input
              v-model="formData.commandCenter"
              placeholder="请输入指挥部/现场指挥部"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="角色" prop="role">
            <el-input
              v-model="formData.role"
              placeholder="请输入角色"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="完成时间" prop="completionTime">
            <el-input
              v-model="formData.completionTime"
              placeholder="请输入完成时间，如：30分钟内"
              maxlength="50"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="职责/任务" prop="responsibility">
            <el-input
              v-model="formData.responsibility"
              type="textarea"
              :rows="4"
              placeholder="请输入职责/任务描述"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="form-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        确定
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "CommandConfigurationForm",
  props: {
    isEditMode: {
      type: Boolean,
      default: false,
    },
    editingRow: {
      type: Object,
      default: null,
    },
    orgOptions: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      submitting: false,
      formData: {
        accidentType: "",
        accidentSubType: "",
        responseCommand: "",
        orgId: "",
        orgName: "",
        commandCenter: "",
        role: "",
        completionTime: "",
        responsibility: "",
      },
      formRules: {
        accidentType: [
          { required: true, message: "请输入事故类型", trigger: "blur" },
        ],
        accidentSubType: [
          { required: true, message: "请输入事故小类", trigger: "blur" },
        ],
        responseCommand: [
          { required: true, message: "请输入响应指令", trigger: "blur" },
        ],
        orgId: [
          {
            required: true,
            message: "请选择机构/单位/名称",
            trigger: "change",
          },
        ],
        commandCenter: [
          {
            required: true,
            message: "请输入指挥部/现场指挥部",
            trigger: "blur",
          },
        ],
        role: [{ required: true, message: "请输入角色", trigger: "blur" }],
        completionTime: [
          { required: true, message: "请输入完成时间", trigger: "blur" },
        ],
        responsibility: [
          { required: true, message: "请输入职责/任务", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    editingRow: {
      handler(newVal) {
        if (newVal) {
          this.initFormData();
        } else {
          this.resetForm();
        }
      },
      immediate: true,
    },
  },
  methods: {
    initFormData() {
      if (this.isEditMode && this.editingRow) {
        this.formData = {
          accidentType: this.editingRow.accidentType || "",
          accidentSubType: this.editingRow.accidentSubType || "",
          responseCommand: this.editingRow.responseCommand || "",
          orgId: this.editingRow.orgId || "",
          orgName: this.editingRow.orgName || "",
          commandCenter: this.editingRow.commandCenter || "",
          role: this.editingRow.role || "",
          completionTime: this.editingRow.completionTime || "",
          responsibility: this.editingRow.responsibility || "",
        };
      } else {
        this.resetForm();
      }
    },

    resetForm() {
      this.formData = {
        accidentType: "",
        accidentSubType: "",
        responseCommand: "",
        orgId: "",
        orgName: "",
        commandCenter: "",
        role: "",
        completionTime: "",
        responsibility: "",
      };
      if (this.$refs.formRef) {
        this.$refs.formRef.clearValidate();
      }
    },

    handleOrgChange(value) {
      const selectedOrg = this.orgOptions.find((item) => item.value === value);
      this.formData.orgName = selectedOrg ? selectedOrg.label : "";
    },

    handleCancel() {
      this.$emit("cancel");
    },

    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.submitting = true;

          // 模拟提交延迟
          setTimeout(() => {
            this.submitting = false;
            this.$emit("confirm", { ...this.formData });
          }, 500);
        } else {
          this.$message.error("请完善表单信息");
          return false;
        }
      });
    },
  },
};
</script>

<style scoped>
.command-configuration-form {
  padding: 20px;
}

.command-form {
  padding: 10px 0;
}

.form-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.form-footer .el-button {
  width: 100px;
  margin: 0 10px;
}

::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.4;
}

::v-deep .el-input,
::v-deep .el-select,
::v-deep .el-textarea {
  width: 100%;
}

::v-deep .el-textarea__inner {
  resize: vertical;
}
</style>
