<!-- 工作内容管理---WorkContentManagement -->
 <template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="通知类型" prop="noticeType">
          <el-select :disabled="styleType === 3" v-model="form.noticeType" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in noticeTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="通知标题" prop="title">
          <el-input :disabled="styleType === 3" style="width: 400px;" v-model="form.title" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接收对象" prop="receiveList">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 400px;"
            v-model="form.receiveList"
            :options="orgTree"
            :props="props"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleReceiveChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="通知内容" prop="content">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 400px;" 
            v-model="form.content" 
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            placeholder="请输入" />
        </el-form-item>

        <el-form-item label="是否需要反馈" prop="feedback">
          <el-select :disabled="styleType === 3" v-model="form.feedback" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.feedback === '0'" label="反馈单位选择" prop="feedbackList">
          <el-cascader
            style="width: 400px;"
            v-model="form.feedbackList"
            :options="selectOrgData"
            :props="props"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleFeedbackChange">
          </el-cascader>
        </el-form-item>
        <el-form-item v-if="form.feedback === '0'" label="是否需要反馈材料" prop="feedbackData">
          <el-select :disabled="styleType === 3" v-model="form.feedbackData" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- <el-form-item label="发布时间" prop="publishTime">
          <el-date-picker
            :disabled="styleType === 3"
            v-model="form.publishTime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择时间"
            style="width: 400px;"
            type="datetime"
            @change="handleChangeMonth">
          </el-date-picker>
        </el-form-item> -->

        <el-form-item label="推送渠道" prop="pushChannelList">
          <el-select :disabled="styleType === 3" v-model="form.pushChannelList" multiple placeholder="请选择" style="width: 400px;">
            <el-option
              v-for="item in pushChannelTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="附件信息" prop="fileList">
          <el-upload
            :disabled="styleType === 3"
            ref="uploadRef"
            class="upload-demo"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :http-request="uploadFile"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button v-if="styleType != 3" size="small" type="primary">点击上传</el-button>
            <div v-if="styleType != 3" slot="tip" class="el-upload__tip">
              只能上传1个文件，且不超过10M
            </div>
          </el-upload>
        </el-form-item>

      </el-form>
    </general-dialog>

    <!-- <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, dutyManagementApi, orgApi } from "@/api";
import { getItemList, workNoticeType } from "@/utils/dictionary";

import { conversionDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "WorkContentManagement",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "title",
          label: "通知标题",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          label: "类型",
          prop: "noticeType",
          type: "select",
          placeholder: "请选择",
          width: "150",
          options: [],
        },
      ],
      columns: [
        { prop: "title", label: "通知标题", text: true },
        { prop: "noticeTypeName", label: "类型", text: true, width: "160px" },
        { prop: "publishTime", label: "发布时间", text: true, width: "160px" },
        { prop: "status", label: "当前环节", text: true, width: "120px" },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '350px',
          operationList: [
           {
              label: '编辑',
              permission: 'noticeManage:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                if(this.tableData[$index].status === '待发布'){
                    return true
                }else{
                    return false
                }
              }
            },
            {
              label: '发布',
              permission: 'noticeManage:publish',
              buttonClick: this.handlePublish,
              isShow: (row, $index) => {
                if(this.tableData[$index].status === '待发布'){
                    return true
                }else{
                    return false
                }
              }
            },
            {
              label: '归档',
              permission: 'noticeManage:remove',
              buttonClick: this.handleRemovePublish,
              isShow: (row, $index) => {
                if(this.tableData[$index].status === '已发布'){
                    return true
                }else{
                    return false
                }
              }
            },
            {
              label: '删除',
              permission: 'noticeManage:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '详情',
              permission: 'noticeManage:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      noticeTypeList: [],
      trueOrFalseTypeList:[
        {itemName: "是", id: "0"},
        {itemName: "否", id: "1"}
      ],
      pushChannelTypeList: [
        {itemName: "内部系统推送", id: "1"},
        {itemName: "信息推送", id: "2"},
        {itemName: "电子邮件推送", id: "3"}
      ],

      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增工作通知内容",

      // 单位列表
      orgTree:[],
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      selectOrgData: [],
      // feedbackList:[],

      form: {
        noticeType: "",
        title: "",
        receiveList: [],
        content: "",
        feedback: "1",
        feedbackList: "",
        feedbackData: "1",
        publishTime: "",
        pushChannelList: [],
        fileList:[]
      },
      rules: {
        noticeType: [
          {required: true, message: '通知类型不能为空', trigger: 'blur'}
        ],
        title: [
          {required: true, message: '通知标题不能为空', trigger: 'blur'}
        ],
        receiveList: [
          {required: true, message: '接收对象不能为空', trigger: 'blur'}
        ],
        feedbackList: [
          {required: true, message: '反馈单位不能为空', trigger: 'blur'}
        ],
        publishTime: [
          {required: true, message: '发布时间不能为空', trigger: 'blur'}
        ],
        pushChannelList: [
          {required: true, message: '推送渠道不能为空', trigger: 'blur'}
        ]
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleChangeMonth(value) {
      this.form.publishTime = conversionDate(value);
    },
    handleReceiveChange(value) {
      // 选择的接收对象集合
      const checkedNodes = this.$refs.orgCascaderRef.getCheckedNodes(true);
      console.log('xc12090129102',checkedNodes);
      
      this.selectOrgData = checkedNodes.map((node) => node.data);
      console.log('xc12090129102',this.selectOrgData);
      this.form.receiveList =  this.selectOrgData.map((node) => node.id);
      console.log('xc12090129102',this.form.receiveList);
      // this.selectOrgData.forEach(nodes => {
      //     receiveList.push(nodes.id)
      // })
    },
    handleFeedbackChange(value) {
      // 选择的反馈对象集合
      this.form.feedbackList = value;
      console.log("xc99999999", this.form.feedbackList);
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.noticeTypeList = await getItemList(workNoticeType);
        this.searchItems[1].options = this.noticeTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      dutyManagementApi.queryContentManageById({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        this.form = {
          ...data,
        };

        if (this.form.feedback === "0") {
          setTimeout(() => {
            // 回显反馈单位
            const checkedNodes = this.$refs.orgCascaderRef.getCheckedNodes(true);
            this.selectOrgData = checkedNodes.map((node) => node.data);
          }, 100);
        }
      });
    },

    //新增
    handleAdd() {
      this.queryOrgTreeDataList();
      this.styleType = 1;
      this.dialogVisible = true;
      // this.form = {};
      this.resetFormData();
      this.generalDialogTitle = "新增工作通知内容";
    },

    //编辑
    handleEdit(row) {
      this.queryOrgTreeDataList();
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑工作通知内容";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看工作通知内容";
    },

    // 发布
    handlePublish(row) {
      dutyManagementApi.publishWorkNotice({ id: row.id, status: "2" }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.handSubmitSuccess();
        this.$message.success("发布成功！");
      });
      // setTimeout(() => {
      //   this.$message.success("发布成功！");
      // }, 500);
    },

    // 归档
    handleRemovePublish(row) {
      dutyManagementApi.publishWorkNotice({ id: row.id, status: "3" }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.handSubmitSuccess();
        this.$message.success("归档成功！");
      });
      // setTimeout(() => {
      //   this.$message.success("归档成功！");
      // }, 500);
    },

    //删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dutyManagementApi.deleteContentManage({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('删除成功');
          this.handSubmitSuccess();
        })
      })
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await dutyManagementApi.queryContentManagePage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await dutyManagementApi.createContentManage(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await dutyManagementApi.updateContentManage(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      // this.$refs.addForm.resetFields();
      this.resetFormData();
    },

    resetFormData() {
      this.form = {
        noticeType: "",
        title: "",
        receiveList: [],
        content: "",
        feedback: "",
        feedbackList: "",
        feedbackData: "",
        publishTime: "",
        pushChannelList: [],
        fileList:[]
      };
      this.selectOrgData = [];
      this.fileList = [];
      this.form.fileList = [];
    },

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl ='http://' + getIPAddress() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
      } 
      window.open(fileUrl, '_blank');
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    // // 关闭预览框
    // handleCloseIframe() {
    //   this.showIframe = false;
    // },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
