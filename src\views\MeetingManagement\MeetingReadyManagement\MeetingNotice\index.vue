<!-- 会议通知---MeetingNotice -->
<template>
  <div class="user-index-container">
    <portal-table
      v-if="showMeetingType === 1"
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
      :button-list="buttonList"
      @buttonClick="handleButtonClick"
    >
      <portal-table
        v-if="styleType === 0"
        style="padding: 60px"
        :tableHeight="430"
        :showAddButton="false"
        :showSelection="true"
        :isAllSelect="false"
        :columns="flowColumns"
        :show-pagination="false"
        :table-data="flowTableData"
        row-key="id"
        @handle-selection-change="handleFlowSelectionChange"
      />

      <el-form
        v-if="styleType != 0"
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
        <el-form-item label="会议名称" prop="meetingName">
          <el-input :disabled="styleType === 3" style="width: 281px;" v-model="form.meetingName" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="会议日期" prop="meetingDate">
          <el-date-picker
            :disabled="styleType === 3"
            v-model="form.meetingDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeMeetingDate">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="调试时间" prop="deTime">
          <el-time-picker
            :disabled="styleType === 3"
            style="width: 281px;"
            v-model="form.deTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择调试时间">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="召开时间" prop="holdTime">
          <el-time-picker
            :disabled="styleType === 3"
            style="width: 281px;"
            v-model="form.holdTime"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="选择召开时间">
          </el-time-picker>
        </el-form-item>

        <el-form-item label="报备截止时间" prop="deadlineDate">
          <el-date-picker
            :disabled="styleType === 3"
            v-model="form.deadlineDate"
            format="yyyy-MM-dd"
            placeholder="选择时间"
            style="width: 281px;"
            type="datetime"
            @change="handleChangeDeadline">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否使用会控" prop="meetingControls">
          <el-select :disabled="styleType === 3" v-model="form.meetingControls" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议周期" prop="meetingCycle">
          <el-select :disabled="styleType === 3" v-model="form.meetingCycle" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingCycleList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="会议终端类型" prop="meetingTerminalType">
          <el-select :disabled="styleType === 3" v-model="form.meetingTerminalType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTerminalTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议类别" prop="meetingType">
          <el-select :disabled="styleType === 3" v-model="form.meetingType" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="主会场信息" prop="venueInfo">
          <el-select :disabled="styleType === 3" v-model="form.venueInfo" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueInfoList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主会场需求" prop="venueRequirement">
          <el-select :disabled="styleType === 3" v-model="form.venueRequirement" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in venueRequirementList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="本地使用终端" prop="locallyTerminal">
          <el-select :disabled="styleType === 3" v-model="form.locallyTerminal" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in locallyTerminalList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="会议级别" prop="meetingLevel">
          <el-select :disabled="styleType === 3" v-model="form.meetingLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in meetingLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="分会场发言" prop="subVenueSpeeches">
          <el-select :disabled="styleType === 3" v-model="form.subVenueSpeeches" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in subVenueSpeechesList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会单位" prop="attendUnitList">
          <el-cascader
            ref="orgCascaderRef"
            style="width: 600px;"
            v-model="form.attendUnitList"
            :options="orgTree"
            :props="props"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleAttendUnitChange">
          </el-cascader>
        </el-form-item>


        <el-form-item label="领导级别" prop="leadershipLevel">
          <el-select :disabled="styleType === 3" v-model="form.leadershipLevel" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in leadershipLevelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="参会领导" prop="attendLeader">
          <el-input :disabled="styleType === 3" style="width: 281px;" v-model="form.attendLeader" placeholder="请输入" />
        </el-form-item>


        <el-form-item label="承办单位" prop="organUnit">
          <el-cascader
            style="width: 281px;"
            v-model="form.organUnit"
            :options="orgTree"
            :props="organUnitProps"
            :disabled="styleType == 3"
            collapse-tags
            clearable
            :show-all-levels="false"
            @change="handleArganUnitChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone">
          <el-input :disabled="styleType === 3" style="width: 281px;" v-model="form.phone" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="保障人员" prop="supportPersonnel">
          <el-select :disabled="styleType === 3" v-model="form.supportPersonnel" placeholder="请选择" style="width: 600px;">
            <el-option
              v-for="item in supportPersonnelList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="通知附件" prop="fileList">
          <el-upload
            style="width: 600px;"
            :disabled="styleType === 3"
            ref="uploadRef"
            class="upload-demo"
            action=""
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :before-remove="beforeRemove"
            :http-request="uploadFile"
            multiple
            :limit="1"
            :on-exceed="handleExceed"
            :file-list="fileList"
          >
            <el-button v-if="styleType != 3" size="small" type="primary">点击上传</el-button>
            <div v-if="styleType != 3" slot="tip" class="el-upload__tip">
              提示：上传通知文件后，该文件将发送至参会单位。文件格式支持xlsx、docx、pptx、ppt。文件大小不能超过100M。
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="会议说明" prop="meetingNotes">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 600px;" 
            v-model="form.meetingNotes" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input 
            :disabled="styleType === 3" 
            style="width: 600px;" 
            v-model="form.remark" 
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 8}"
            placeholder="请输入" />
        </el-form-item>


        <el-form-item label="通知方式" prop="noticeMethodList">
          <el-select :disabled="styleType === 3" v-model="form.noticeMethodList" multiple placeholder="请选择" style="width: 600px;">
            <el-option
              v-for="item in noticeMethodTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否会议录播" prop="meetingRecorded">
          <el-select :disabled="styleType === 3" v-model="form.meetingRecorded" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否会议报名" prop="meetingRegister">
          <el-select :disabled="styleType === 3" v-model="form.meetingRegister" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="是否会议提醒" prop="meetingReminder">
          <el-select :disabled="styleType === 3" v-model="form.meetingReminder" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="form.meetingReminder === 1" label="会议提醒" prop="meetingReminderTime">
          <el-input :disabled="styleType === 3" style="width: 231px;" v-model="form.meetingReminderTime" placeholder="请输入提前多少分钟提醒" /> 分钟
        </el-form-item>

        <br>
        <el-form-item label="设置发送时间" prop="setSendingTime">
          <el-select :disabled="styleType === 3" v-model="form.setSendingTime" placeholder="请选择" style="width: 281px;">
            <el-option
              v-for="item in setSendingTimeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id" >
            </el-option>
          </el-select>
        </el-form-item>        
        <el-form-item v-if="form.setSendingTime != 0" label="发送时间" prop="sendingTime">
          <el-input :disabled="styleType === 3" style="width: 231px;" v-model="form.sendingTime" placeholder="请输入审批后多少分钟发送" /> 分钟
        </el-form-item>

        
      </el-form>
    </general-dialog>

    <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog>

    <reportMeeting 
      ref="reportMeetingRef"
      @buttonBackClick="handleReportBack()"> 
    </reportMeeting>
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { systemManagementApi, orgApi, meetingManagementApi } from "@/api";
import { getItemList, inspectionDictionaryType, inspectionResultType } from "@/utils/dictionary";

import { conversionDateNotSecond, getCurrentDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 
import reportMeeting from "./components/reportMeeting.vue";

export default {
  name: "MeetingNotice",
  components: {
    GeneralDialog,
    PortalTable,
    reportMeeting,
  },
  data() {
    return {
      showMeetingType: 1,
      searchItems: [
        {
          prop: "meetingName",
          label: "会议名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingTime",
          label: "会议日期",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "meetingStartTime",
          label: "召开时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "meetingEndTime",
          label: "截止时间",
          type: "startEndPicker",
          startProp: "startTime", // 开始时间字段
          endProp: "endTime", // 结束时间字段
          startPlaceholder: "请选择开始时间",
          endPlaceholder: "请选择结束时间",
          width: "260"
        },
        {
          prop: "meetingDescribe",
          label: "会议说明",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "meetingLoction",
          label: "主会场信息",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "meetingType",
          label: "会议终端类型",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "meetingGuaranteeUser",
          label: "保障人员",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "inspectionType",
          label: "类别",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "meetingName", label: "会议名称", text: true },
        { prop: "meetingDate", label: "会议日期", text: true },
        { prop: "holdTime", label: "召开时间", text: true },
        { prop: "deadlineDate", label: "报备截止时间", text: true },
        { prop: "meetingNotes", label: "会议说明", text: true },
        { prop: "venueInfo", label: "主会场信息", text: true },
        { prop: "meetingTerminalType", label: "会议终端类型", text: true },
        { prop: "supportPersonnel", label: "保障人员", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '260px',
          operationList: [
            {
              label: '审核',
              permission: 'dutyCheck:audit',
              buttonClick: this.handleAudit,
              isShow: (row, $index) => {
                if(this.tableData[$index].auditFlag === 1){
                    return false
                }else{
                    return true
                }
              }
            },
            {
              label: '编辑',
              permission: 'dutyCheck:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'dutyCheck:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '详情',
              permission: 'dutyCheck:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '报送信息',
              permission: 'dutyCheck:report',
              buttonClick: this.handleApproval,
              isShow: (row, $index) => {
                if(this.tableData[$index].inspectionResult === 0){
                    return false
                }else{
                    return true
                }
              }
            }
          ]
        }
      ],
      tableData: [],
      flowColumns: [
        { prop: "flowName", label: "会议流程名称", text: true },
        { prop: "meetingTypeName", label: "类型名称", text: true },
      ],
      flowTableData: [],
      selectedFlowData: null,

      styleType: 1, //1：新增，2：编辑，3：查看
      trueOrFalseTypeList:[
        {itemName: "是", id: 1 },
        {itemName: "否", id: 0 }
      ],
      meetingTerminalTypeList: [
        {itemName: "测试应急终端", id: "测试应急终端"},
        {itemName: "测试指挥终端", id: "测试指挥终端"},
      ],
      venueInfoList: [
        {itemName: "测试主会场1", id: "测试主会场1"},
        {itemName: "测试主会场2", id: "测试主会场2"},
      ],
      venueRequirementList: [
        {itemName: "测试主会场需求1", id: "测试主会场需求1"},
        {itemName: "测试主会场需求2", id: "测试主会场需求2"},
      ],
      locallyTerminalList: [
        {itemName: "172.2.1.10", id: "172.2.1.10"},
        {itemName: "172.2.1.11", id: "172.2.1.11"},
      ],
      meetingLevelList: [
        {itemName: "一类会议", id: "1"},
        {itemName: "二类会议", id: "2"},
        {itemName: "三类会议", id: "3"},
        {itemName: "四类会议", id: "4"},
      ],
      subVenueSpeechesList: [
        {itemName: "市应急指挥中心", id: "市应急指挥中心"},
        {itemName: "丰台区应急指挥中心", id: "丰台区应急指挥中心"},
        {itemName: "西城区应急管理局", id: "西城区应急管理局"},
        {itemName: "东城区应急管理局", id: "东城区应急管理局"},
      ],
      leadershipLevelList: [
        {itemName: "局级", id: "局级"},
        {itemName: "副局级", id: "副局级"},
        {itemName: "处级", id: "处级"},
        {itemName: "副处级", id: "副处级"},
        {itemName: "正科级", id: "正科级"},
        {itemName: "副科级", id: "副科级"},
      ],
      supportPersonnelList: [
        {itemName: "张三（18090900909）", id: "张三（18090900909）"},
        {itemName: "李四（18090900909）", id: "李四（18090900909）"},
        {itemName: "王五（18090900909）", id: "王五（18090900909）"},
        {itemName: "赵六（18090900909）", id: "赵六（18090900909）"},
      ],
      setSendingTimeList: [
        {itemName: "审批后立即发送", id: "0"},
        {itemName: "审批后选择时间发送", id: "1"},
      ],

      meetingCycleList:[
        {itemName: "1天", id: "1"},
        {itemName: "7天", id: "7"},
        {itemName: "15天", id: "15"},
        {itemName: "一个月", id: "30"},
      ],
      noticeMethodTypeList: [
        {itemName: "系统消息", id: "1"},
        {itemName: "短信", id: "2"},
        {itemName: "语音广播", id: "3"},
        {itemName: "京办", id: "4"},
      ],
      meetingReminderList: [
        {itemName: "提前5分钟", id: "5"},
        {itemName: "提前10分钟", id: "10"},
        {itemName: "提前15分钟", id: "15"},
        {itemName: "提前30分钟", id: "30"},
        {itemName: "自定义时间", id: "0"},
      ],
      orgTree: [],
      organUnitProps: {
        multiple: false,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      props: {
        multiple: true,
        label: 'orgName',
        value: 'id',
        children: 'children',
        emitPath: false 
      },
      meetingTypeList: [],


      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "760px",
      generalDialogTitle: "新增会议通知",
      buttonList: [
        { id: 1, text: "不使用会议流程", type: "primary", width: "150px" },
      ],

      form: {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        sendingTime: "",
        setSendingTime: "",
        fileList:[]
      },
      rules: {
        meetingName: [
          {required: true, message: '会议名称不能为空', trigger: 'blur'}
        ],
        meetingType: [
          {required: true, message: '会议类别不能为空', trigger: 'blur'}
        ],
        meetingControls: [
          {required: true, message: '是否使用会控不能为空', trigger: 'blur'}
        ],
        meetingCycle: [
          {required: true, message: '会议周期不能为空', trigger: 'blur'}
        ],
        organUnit: [
          {required: true, message: '承办单位不能为空', trigger: 'blur'}
        ],
        attendUnitList: [
          {required: true, message: '参会单位不能为空', trigger: 'blur'}
        ],
        noticeMethodList: [
          {required: true, message: '通知方式不能为空', trigger: 'blur'}
        ],
        meetingRecorded: [
          {required: true, message: '是否会议录播不能为空', trigger: 'blur'}
        ],
        meetingRegister: [
          {required: true, message: '是否会议报名不能为空', trigger: 'blur'}
        ],
        meetingAlert: [
          {required: true, message: '是否会议提醒不能为空', trigger: 'blur'}
        ],
      },
    };
  },
  mounted() {
    this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleFlowSelectionChange(flowData) {
      console.log(flowData);
      // 选择会议流程
      this.selectedFlowData = flowData[0];
    },
    handleChangeMeetingDate(value) {
      this.form.meetingDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleChangeDeadline(value) {
      this.form.deadlineDate = getCurrentDate("YYYY-MM-DD" ,value);
    },
    handleArganUnitChange(value) {
      // 选择的承办单位
    },
    handleAttendUnitChange(value) {
      // 选择的参会单位集合
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        const res = await meetingManagementApi.queryMeetingTypeList();
        const { code, data, message, error } = res;
        if (code === 0) {
          this.meetingTypeList = data;
          // this.searchItems[0].options = this.meetingTypeList.map((item) => ({
          //   label: item.itemName,
          //   value: item.id
          // }))
        }
      } catch (error) {
        this.$message.error(error.message);
      }

      // try {
      //   this.inspectionResultList = await getItemList(inspectionResultType);
      //   this.searchItems[2].options = this.inspectionResultList.map((item) => ({
      //     label: item.itemName,
      //     value: item.id
      //   }))
      // } catch (error) {
      //   this.$message.error(error.message);
      // }
    },

    // 查询单位列表
    async queryOrgTreeDataList() {
      const res = await orgApi.queryOrgTree();
      const { code, data, message, error } = res;
      this.orgTree = this.handleOrgTreeData(data);
    },
    handleOrgTreeData(orgData) {
      return orgData.map(item => {
        // 深拷贝当前节点（避免修改原对象）
        const newNode = {...item};
        
        // 如果 children 存在且是数组
        if (Array.isArray(newNode.children)) {
          if (newNode.children.length === 0) {
            // 空数组设置为 null
            newNode.children = null;
          } else {
            // 递归处理子节点
            newNode.children = this.handleOrgTreeData(newNode.children);
          }
        }
        return newNode;
      });
    },

    //查看详情
    getRowDataInfo(row) {
      this.fileList = [];
      this.form.fileList = [];
      meetingManagementApi.queryMeetingNoticeDetail({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        if (data.fileList && data.fileList.length > 0) {
          data.fileList.forEach((row) => {
            this.fileList.push({
              name: row.fileName,
              url: this.fileBaseUrl + row.fileUrl,
              id: row.id,
            });
          });
        } else {
          data.fileList = [];
        }
        this.form = {
          ...data,
        };
      });
    },

    handleButtonClick(item) {
      if (item.id === 1) {
        this.queryOrgTreeDataList();
        this.styleType = 1;
        this.dialogVisible = true;
        // this.form = {};
        this.resetFormData();
        this.fileList = [];
        this.form.fileList = [];
        this.generalDialogTitle = "新增会议通知";
        this.buttonList = [];
      } else if (item.id === 2) {
        // this.handleSubmit(1);
      }
    },
    //新增
    async handleAdd() {
      this.styleType = 0;
      this.dialogVisible = true;
      // this.form = {};
      // this.fileList = [];
      // this.form.fileList = [];
      this.generalDialogTitle = "选择会议流程";
      this.buttonList = [{ id: 1, text: "不使用会议流程", type: "primary", width: "150px" }];
      const res = await meetingManagementApi.queryMeetingFlowList();
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.flowTableData = data || [];
    },

    //编辑
    handleEdit(row) {
      this.queryOrgTreeDataList();
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑会议通知";
    },

    //查看
    handleReview(row) {
      this.queryOrgTreeDataList();
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看会议通知";
    },

    //删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        meetingManagementApi.deleteMeetingNotice({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('删除成功');
          this.handSubmitSuccess();
        })
      })
    },

    // 审核
    handleAudit(row) {
      this.$confirm('确定审核通过此会议通知, 是否继续?', '审核', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        meetingManagementApi.auditMeeting({ id: row.id, auditFlag: 1 }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('审核通过');
          this.handSubmitSuccess();
        })
      })
    },

    // 报送
    handleApproval(row) {
      // this.$message.success("报送成功！");
      this.showMeetingType = 2;
      this.$refs.reportMeetingRef.showReportInfoList(row.id, row.meetingName);
    },

    handleReportBack() {
      this.showMeetingType = 1;
    },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await meetingManagementApi.queryMeetingNoticePage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.meetingDate && row.meetingDate.length > 0) {
        row.startTime = conversionDateNotSecond(row.meetingDate[0])
        row.endTime = conversionDateNotSecond(row.meetingDate[1])
        delete row.meetingDate
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.resetFormData();
      // this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      if (this.styleType === 0) {
        this.getFlowDetailData();
        return;
      }
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await meetingManagementApi.createMeetingNotice(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await meetingManagementApi.updateMeetingNotice(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          this.$alert('请填写所有必填字段！', '提示', {
            confirmButtonText: '确定',
          });
          return false;
        }
      });
    },

    // 获取当前选择的会议流程详情数据
    getFlowDetailData() {
      if (!this.selectedFlowData || !this.selectedFlowData.id) {
        this.$message.warning("请选择一个会议流程！");
        return
      }
      meetingManagementApi.queryMeetingFlowDetail({ id: this.selectedFlowData.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.resetFormData();
        this.form.meetingType = data.meetingType;
        this.form.attendUnitList = data.attendUnitList;
        this.form.organUnit = data.organUnit;
        this.form.meetingCycle = data.meetingCycle;
        this.form.meetingControls = data.meetingControls;
        this.form.noticeMethodList = data.noticeMethodList;
        this.form.meetingRegister = data.meetingRegister;
        this.form.meetingReminder = data.meetingReminder;
        this.form.meetingReminderTime = data.meetingReminderTime;
        this.form.meetingRecorded = data.meetingRecorded;
        this.styleType = 1;
        this.queryOrgTreeDataList();
        this.fileList = [];
        this.form.fileList = [];
        this.generalDialogTitle = "新增会议通知";
        this.buttonList = null;
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.resetFormData();
      // this.$refs.addForm.resetFields();
    },

    resetFormData() {
      this.form = {
        meetingName: "",
        meetingDate: "",
        deTime: "",
        holdTime: "",
        deadlineDate: "",
        meetingControls: "",
        meetingCycle: "",
        meetingTerminalType: "",
        meetingType: "",
        venueInfo: "",
        venueRequirement: "",
        locallyTerminal: "",
        subVenueSpeeches: "",
        attendUnitList: [],
        leadershipLevel: "",
        attendLeader: "",
        organUnit: "",
        phone: "",
        meetingNotes: "",
        remark: "",
        noticeMethodList: [],
        meetingRecorded: "",
        meetingRegister: "",
        meetingReminder: "",
        meetingReminderTime: "",
        setSendingTime: "",
        sendingTime: "",
        fileList:[]
      };
    },

    // 新增上传方法
    uploadFile(file) {
      // 文件大小校验
      this.fileList = [];
      const MAX_SIZE = 100 * 1024 * 1024; // 100MB
      if (file.file.size > MAX_SIZE) {
        this.$message.error("文件大小超过100MB限制");
        this.$refs.uploadRef.clearFiles();
        return;
      }
      const formData = new FormData();
      formData.append("file", file.file);

      systemManagementApi.uploadFile(formData).then((res) => {
        this.$refs.uploadRef.clearFiles();
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success("上传成功");
        setTimeout(() => {
          const fileUrl = this.fileBaseUrl + data.fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileUrl = fileUrl;
          // data.fileType = getFileExtension(data.url);

          this.form.fileList.push(data);
          this.fileList.push({
            name: data.fileName,
            url: fileUrl,
            id: data.id,
          });
        }, 500);
      });
    },
    handleRemove(file, fileList) {
      this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    },
    handlePreview(file) {
      let fileUrl = file.url;
      const fileExtension = fileUrl.split(".").pop().toLowerCase();
      const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
      if (!previewableExtensions.includes(fileExtension)) {
        // 如果文件类型不支持直接预览，则重新拼接URL
        fileUrl ='http://' + getIPAddress() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
      } 
      // window.open(fileUrl, '_blank');
      this.preFileUrl = fileUrl;
      this.showIframe = true;
    },
    handleExceed(files, fileList) {
      this.$message.warning("只能上传一个文件");
      // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },

    // 关闭预览框
    handleCloseIframe() {
      this.showIframe = false;
    },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
  .el-upload__tip {
    color: red;
    line-height: normal !important;
  }
}
</style>
