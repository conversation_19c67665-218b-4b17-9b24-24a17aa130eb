<template>
  <div class="duty-evaluation-statistics-container">
    <div v-show="dutyVisible">
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <el-form
              :inline="true"
              :model="searchData"
              class="demo-form-inline"
            >
              <el-form-item label="单位名称">
                <el-input
                  v-model="searchData.orgName"
                  placeholder="请输入单位名称"
                ></el-input>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  @click="onSubmit"
                  >高级筛选</el-button
                >
                <el-button
                  type="primary"
                  icon="el-icon-refresh"
                  @click="onReset"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <div class="Statistics">
              <div
                class="Statistics-item"
                v-for="(item, index) in StatisticsData"
                :key="item.title"
              >
                <div
                  class="vertical-line"
                  :class="`bg-color-${index % 4}`"
                ></div>
                <div class="content">
                  <div class="image">
                    <el-image
                      :src="
                        require(`@/assets/images/statistics_${index + 1}.png`)
                      "
                    />
                  </div>
                  <div class="content-text">
                    <div class="title">
                      {{ item.title }}
                    </div>
                    <div class="data">
                      <div class="number">{{ item.data }}</div>
                    </div>
                    <div
                      class="percentage"
                      :style="{ color: getPercentageColor(item.percentage) }"
                    >
                      {{ item.percentage }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <div class="table-title">评价详情列表</div>
            <div class="table-container">
              <PortalTable
                :tableHeight="248"
                :columns="columns"
                :table-data="tableData"
                :showSelection="false"
                :showIndex="false"
                row-key="id"
                :pagination="pagination"
                :showAddButton="false"
                @handle-size-change="handleSizeChange"
                @handle-current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="30">
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">地区评分分布</div>
            <el-divider content-position="right">
              <el-radio-group
                @change="changeScoreRadio"
                v-model="scoreRadio"
                size="small"
              >
                <el-radio-button label="平均分"></el-radio-button>
                <el-radio-button label="最高分"></el-radio-button>
                <el-radio-button label="最低分"></el-radio-button>
              </el-radio-group>
            </el-divider>
            <div class="chart-container">
              <echarts-component :options="scoreOptions" />
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover">
            <div class="chart-title">评价等级统计</div>
            <el-divider></el-divider>
            <div class="chart-container">
              <div class="left">
                <echarts-component :options="typeOptions" />
              </div>
              <div class="right">
                <div class="percentage">
                  <div
                    class="percentage-item"
                    v-for="item in percentageData"
                    :key="item.title"
                  >
                    <div class="percentage-text">
                      <div class="title">{{ item.title }}</div>
                      <div class="number">{{ item.number }}%</div>
                    </div>
                    <el-progress
                      :show-text="false"
                      :stroke-width="10"
                      :percentage="item.number"
                      :color="item.color"
                    ></el-progress>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <PublicDialog ref="publicDialogRef" @dutyShow="dutyShow" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import echartsComponent from "@/components/echarts.vue";
import PublicDialog from "@/views/ComprehensiveAssessment/ComprehensiveEvaluationManagement/components/publicDialog.vue";
import * as echarts from "echarts/core";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
export default {
  name: "DutyEvaluationStatistics",
  components: {
    PortalTable,
    echartsComponent,
    PublicDialog,
  },
  data() {
    return {
      dutyVisible: true,
      searchData: {
        orgName: "",
      },
      StatisticsData: [
        {
          title: "平均得分",
          data: "92.5",
          percentage: "3.2%",
        },
        {
          title: "参与评价单位",
          data: "126",
          percentage: "5",
        },
        {
          title: "自评完成率",
          data: "98.6%",
          percentage: "1.2%",
        },
        {
          title: "系统评分占比",
          data: "65%",
          percentage: "3.2%",
        },
      ],
      completionOptions: {
        // 修改为两种颜色：绿色(#80FFA5)和灰色(#CCCCCC)
        color: ["#80FFA5", "#CCCCCC"],
        title: {
          text: "", // 清空标题（根据原图无标题）
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "#6a7985",
            },
          },
        },
        // 修改图例为两个数据系列
        legend: {
          data: ["自评完成", "系统评价"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            splitLine: {
              show: false,
            },
            // 修改为12个月份
            data: [
              "1月",
              "2月",
              "3月",
              "4月",
              "5月",
              "6月",
              "7月",
              "8月",
              "9月",
              "10月",
              "11月",
              "12月",
            ],
          },
        ],
        yAxis: [
          {
            type: "value",
            // 坐标轴线样式
            alignTicks: true,
            axisLine: {
              show: true,
              lineStyle: {
                color: "#333", // 深色坐标轴
              },
            },
            // 坐标轴标签
            axisLabel: {
              color: "#666",
              fontSize: 12,
            },
            // 禁用网格线（无网格线）
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          // 系统评价（灰色系列）
          {
            name: "系统评价",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#CCCCCC" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [5, 1, 6, 5, 6, 17, 5, 8, 5, 2, 9, 6],
          },
          // 自评完成（绿色系列）
          {
            name: "自评完成",
            type: "line",
            stack: "Total",
            smooth: true,
            lineStyle: {
              width: 0,
            },
            showSymbol: false,
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(141, 204, 171, 0.8)" },
                { offset: 1, color: "rgba(255, 255, 255, 0.9)" },
              ]),
            },
            data: [0, 5, 15, 20, 45, 20, 10, 8, 12, 15, 10, 0], // 模拟峰值数据
          },
        ],
      },
      typeOptions: {
        tooltip: {
          trigger: "item",
          formatter: "{b}: {d}%",
        },
        color: ["#fb788a", "#52c1f5", "#ffc542", "#24d2d3"],
        series: [
          {
            name: "评价等级统计",
            type: "pie",
            radius: ["50%", "75%"],
            avoidLabelOverlap: false,
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 3,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 30, name: "优秀" },
              { value: 25, name: "良好" },
              { value: 25, name: "合格" },
              { value: 20, name: "不及格" },
            ],
          },
        ],
        graphic: [
          {
            type: "text",
            left: "center",
            top: "35%",
            style: {
              text: "84.5",
              fill: "#2d3748",
              fontSize: 30,
              fontWeight: "bold",
            },
          },
          {
            type: "text",
            left: "center",
            top: "55%",
            style: {
              text: "平均分",
              fill: "#718096",
              fontSize: 15,
              fontWeight: "500",
            },
          },
        ],
      },
      percentageData: [
        {
          title: "优秀",
          number: 30,
          color: "#fb788a",
        },
        {
          title: "良好",
          number: 25,
          color: "#52c1f5",
        },
        {
          title: "合格",
          number: 25,
          color: "#ffc542",
        },
        {
          title: "不及格",
          number: 20,
          color: "#24d2d3",
        },
      ],
      columns: [
        { text: true, prop: "rank", label: "排名" },
        { text: true, prop: "orgName", label: "单位名称" },
        { text: true, prop: "totalSelfRatedScore", label: "自评得分" },
        { text: true, prop: "totalSystemScore", label: "系统得分" },
        { text: true, prop: "totalFinalScore", label: "综合得分" },
        { text: true, prop: "statusFormat", label: "状态" },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "详情",
              permission: "dutyEvaluationStatistics:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [
        {
          ranking: 1,
          region: "东城区",
          department: "应急管理局",
          selfScore: 96.0,
          systemScore: 98.5,
          comprehensiveScore: 97.2,
          grade: "优秀",
          status: "已完成",
          operation: "详情",
        },
        {
          ranking: 2,
          region: "海淀区",
          department: "公安局",
          selfScore: 94.5,
          systemScore: 97.0,
          comprehensiveScore: 95.8,
          grade: "优秀",
          status: "已完成",
          operation: "详情",
        },
        {
          ranking: 3,
          region: "西城",
          department: "消防局",
          selfScore: 93.0,
          systemScore: 96.5,
          comprehensiveScore: 94.8,
          grade: "优秀",
          status: "已完成",
          operation: "详情",
        },
        {
          ranking: 4,
          region: "朝阳区",
          department: "卫生局",
          selfScore: 92.5,
          systemScore: 95.0,
          comprehensiveScore: 93.8,
          grade: "优秀",
          status: "已完成",
          operation: "详情",
        },
        {
          ranking: 5,
          region: "丰台区",
          department: "应急管理局",
          selfScore: 91.0,
          systemScore: 94.5,
          comprehensiveScore: 92.8,
          grade: "良好",
          status: "已完成",
          operation: "详情",
        },
      ],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
      scoreRadio: "平均分",
      scoreOptions: {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          axisLine: {
            show: false, // 隐藏X轴线
          },
          axisTick: {
            show: false, // 隐藏X轴刻度
          },
          splitLine: {
            show: false, // 隐藏X轴网格线
          },
          axisLabel: {
            show: false, // 保留Y轴文字标签
          },
        },
        yAxis: {
          type: "category",
          data: ["海淀", "朝阳", "东城", "西城"],
          axisTick: {
            alignWithLabel: true,
          },
          axisLine: {
            show: false, // 隐藏Y轴线
          },
          axisTick: {
            show: false, // 隐藏Y轴刻度
          },
          axisLabel: {
            show: true, // 保留Y轴文字标签
            margin: 40, // 增加Y轴标签与柱子的距离
          },
        },
        series: [
          {
            name: "Direct",
            type: "bar",
            barWidth: "40%", // 减小柱子宽度以增加间距
            barGap: "30%", // 设置柱子之间的间距
            itemStyle: {
              color: "#6ab7ff", // 修改柱子颜色为蓝色
              borderRadius: [50, 50, 50, 50], // 设置柱子端头为圆角（右侧圆角）
            },
            label: {
              show: true,
              position: "insideRight", // 数值显示在柱子右侧
              formatter: "{c}", // 显示数据值
              color: "#fff", // 文字颜色
              fontSize: 12, // 文字大小
              fontWeight: "bold", // 文字加粗
              offset: [-30, 0], // 向右偏移5像素
            },
            data: [88, 90, 94.5, 97.5],
          },
        ],
      },
      statusList: [
        {
          label: "草稿",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "审核驳回",
          value: 2,
        },
        {
          label: "审核通过",
          value: 3,
        },
        {
          label: "待录入",
          value: 4,
        },
      ],
    };
  },
  methods: {
    queryEvaluationDetailsPage() {
      const params = {
        orgName: this.searchData.orgName,
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
      };
      comprehensiveAssessmentApi
        .queryEvaluationDetailsPage(params)
        .then((res) => {
          if (res.code === 0) {
            this.tableData = res.data.items.map((item) => {
              item.statusFormat =
                this.statusList.find((status) => status.value === item.status)
                  ?.label || "";
              return item;
            });
            this.pagination.total = res.data.total;
          }
        });
    },
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.queryEvaluationDetailsPage();
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.queryEvaluationDetailsPage();
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryEvaluationDetailsPage();
    },
    onReset() {
      this.searchData = {
        orgName: "",
      };
      this.queryEvaluationDetailsPage();
    },
    getPercentageColor(percentage) {
      const value = parseFloat(percentage);
      if (value > 1) return "#67C23A";
      if (value < 1) return "#F56C6C";
      return "#999"; // 等于1时的颜色
    },

    handleDetail(row) {
      this.dutyVisible = false;
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "detail";
      this.$refs.publicDialogRef.title = "评价详情";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    changeScoreRadio(item) {
      console.log(item);
    },
    dutyShow() {
      this.dutyVisible = true;
    },
  },
  mounted() {
    this.queryEvaluationDetailsPage();
  },
};
</script>

<style lang="scss" scoped>
.duty-evaluation-statistics-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
  .demo-form-inline {
    margin-left: 30px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 60px; // 统一控制所有元素间距
  }
  .Statistics {
    display: flex;
    gap: 30px;
    .Statistics-item {
      padding-left: 15px;
      height: 140px;
      display: flex;
      gap: 15px;
      .vertical-line {
        width: 6px;
        height: 120px;
      }
      .bg-color-0 {
        background-color: #007bff; // 蓝色
      }
      .bg-color-1 {
        background-color: #9f47ec; // 绿色
      }
      .bg-color-2 {
        background-color: #0ce63f; // 黄色
      }
      .bg-color-3 {
        background-color: #cc3b4e; // 红色
      }

      .content {
        height: 100px;
        display: flex;
        align-items: center;
        margin-top: 10px;
        gap: 20px;
        .image {
          width: 50px;
          height: 50px;
        }
        .content-text {
          display: flex;
          flex-direction: column;
          gap: 5px;
          border-bottom: 1px solid #3b3a3a;
          width: 200px;
          .title {
            font-size: 15px;
            margin-bottom: 5px;
          }
          .data {
            display: flex;
            gap: 40px;
            align-items: flex-end;
            margin-bottom: 5px;
            .number {
              font-size: 20px;
              font-weight: bold;
              color: #007bff;
            }
            .unit {
              font-size: 10px;
              color: #999;
            }
          }
          .percentage {
            font-size: 15px;
            color: #999;
          }
        }
      }
    }
  }
  .chart-title,
  .table-title {
    font-size: 20px;
    font-weight: bold;
  }
  .chart-container {
    height: 300px;
    display: flex;
    gap: 20px;
    .left {
      width: 40%;
      height: 80%;
    }
    .right {
      width: 35%;
      height: 80%;
      .percentage {
        margin-top: 8%;
        .percentage-item {
          margin-bottom: 10px;
          .percentage-text {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
          }
        }
      }
    }
  }
  .table-container {
    height: 400px;
    margin-top: 30px;
  }
}
::v-deep .el-row {
  margin-top: 30px;
}
</style>