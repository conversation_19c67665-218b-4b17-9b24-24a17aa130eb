import request from "@/utils/request";

// 组织机构管理API
export default {
  // 查询组织机构树形结构
  async queryOrgTree(params = {}) {
    return request({
      url: "/ds/org/queryOrg",
      method: "post",
      data: params,
    });
  },

  // 查询组织机构列表（分页）
  async queryOrgList(params = {}) {
    return request({
      url: "/ds/org/queryOrgList",
      method: "post",
      data: params,
    });
  },

  // 新增组织机构
  async createOrg(data = {}) {
    return request({
      url: "/ds/org/createOrg",
      method: "post",
      data: data,
    });
  },

  // 更新组织机构
  async updateOrg(data = {}) {
    return request({
      url: "/ds/org/updateOrg",
      method: "post",
      data: data,
    });
  },

  // 删除组织机构
  async deleteOrg(data = {}) {
    return request({
      url: "/ds/org/deleteOrg",
      method: "post",
      data: data,
    });
  },

  // 根据ID查询组织机构详情
  async getOrgById(data = {}) {
    return request({
      url: "/ds/org/getOrgById",
      method: "post",
      data: data,
    });
  },

  // 导入组织机构
  async uploadOrg(formData) {
    return request({
      url: "/ds/org/upload",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  // 导出组织机构
  async exportList(formData) {
    return request({
      url: "/ds/org/exportList",
      method: "post",
      data: formData,
      responseType: 'blob',
    });
  },
  

  /* 
  *用户相关接口
  *添加用户
  */
  async createSysUser(params = {}) {
    return request({
      url: "/ds/user/createSysUser",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *修改用户
  */
  async updateSysUser(params = {}) {
    return request({
      url: "/ds/user/updateSysUser",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *查询用户管理列表
  */
  async querySysUserList(params = {}) {
    return request({
      url: "/ds/user/querySysUserList",
      method: "post",
      data: params,
    });
  },

   /* 
  *用户相关接口
  *用户审核
  */
  async auditUserInfo(params = {}) {
    return request({
      url: "/ds/user/auditUserInfo",
      method: "post",
      data: params,
    });
  },

   /* 
  *用户相关接口
  *注销用户
  */
  async disableSysUser(params = {}) {
    return request({
      url: "/ds/user/disableSysUser",
      method: "post",
      data: params,
    });
  },

   /* 
  *用户相关接口
  *启用用户
  */
  async startSysUser(params = {}) {
    return request({
      url: "/ds/user/startSysUser",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *角色分配
  */
  async roleAssignment(params = {}) {
    return request({
      url: "/ds/user/roleAssignment",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *查询角色分配
  */
  async queryRoleAssignment(params = {}) {
    return request({
      url: "/ds/user/queryRoleAssignment",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *获取单个用户详情
  */
  async getUserInfoDetail(params = {}) {
    return request({
      url: "/ds/user/getUserInfoDetail",
      method: "post",
      data: params,
    });
  },

  /* 
  *用户相关接口
  *导入用户列表
  */
  async importSysUserInfoExcel(formData) {
    return request({
      url: "/ds/excel/importSysUserInfoExcel",
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /* 
  *用户相关接口
  *导出用户列表
  */
  async downSysUserInfoExcel(formData) {
    return request({
      url: "/ds/excel/downSysUserInfoExcel",
      method: "post",
      data: formData,
      responseType: 'blob',
    });
  },

};
