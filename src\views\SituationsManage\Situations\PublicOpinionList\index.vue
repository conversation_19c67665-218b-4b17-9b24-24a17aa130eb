<!-- 舆情列表---PublicOpinionList -->
<template>
  <div class="user-index-container">
    <portal-table
      v-if="!isEdit"
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <public-opinion-edit
      v-if="isEdit"
      :public-opinion-data="publicOpinionData"
      :public-opinion-type="publicOpinionType"
      @close="handleClose"
    />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import {
  systemManagementApi,
  dutyManagementApi,
  publicSentimentApi,
} from "@/api";
import {
  getItemList,
  inspectionDictionaryType,
  inspectionResultType,
} from "@/utils/dictionary";

import conversionDate, {
  conversionDateNotSecond,
  getIPAddress,
} from "@/utils/publicMethod";
import { auth } from "@/utils";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import PublicOpinionEdit from "@/views/SituationsManage/Situations/PublicOpinionEdit/index.vue";

export default {
  name: "MeetingNotice",
  components: {
    PublicOpinionEdit,
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      isEdit: false,
      publicOpinionData: {},
      publicOpinionType: "",
      searchItems: [
        {
          prop: "keyWord",
          label: "关键词搜索",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "contentMentionRegionList",
          label: "内容提及地",
          type: "input",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "warningLevel",
          label: "预警等级",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [
            {
              label: "一般",
              value: 1,
            },
            {
              label: "严重",
              value: 2,
            },
            {
              label: "重大",
              value: 3,
            },
          ],
        },
        {
          prop: "inspectionType",
          label: "大屏显示",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [
            {
              label: "是",
              value: 1,
            },
            {
              label: "否",
              value: 0,
            },
          ],
        },
      ],
      columns: [
        { prop: "title", label: "标题", text: true },
        { prop: "warningTags", label: "涉事分类", text: true },
        { prop: "contentMentionRegionList", label: "内容提及地区", text: true },
        { prop: "webName", label: "来源", text: true },
        { prop: "warningLevelText", label: "预警等级", text: true },
        { prop: "warningTime", label: "预警时间", text: true },
        { prop: "isDeleteOriginText", label: "原文是否删除", text: true },
        { prop: "largeScreenDisplay", label: "大屏显示", switch: true },

        {
          action: true, //是否显示操作
          label: "操作",
          width: "260px",
          operationList: [
            {
              label: "编辑",
              permission: "dutyCheck:download",
              buttonClick: (row) => this.handleOperation(row, "edit"),
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "详情",
              permission: "dutyCheck:download",
              buttonClick: (row) => this.handleOperation(row, "detail"),
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      tableData: [],

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    fetchData(searchParams = {}) {
      let params = {
        page: this.pagination.currentPage, // 修正前：pageSize
        count: this.pagination.pageSize, // 修正前：currentPage
        ...searchParams,
      };
      publicSentimentApi.queryWarningInfoList(params).then((res) => {
        const { data } = res;
        if (data && data.items && data.items.length > 0) {
          data.items.forEach((item) => {
            item.contentMentionRegionList =
              item.contentMentionRegionList?.join(",") || "";

            item.warningTime = conversionDate(
              new Date(item.warningTime).toLocaleString()
            );
            item.warningLevelText =
              item.warningLevel === 1
                ? "一般"
                : item.warningLevel === 2
                ? "严重"
                : "重大";
            item.isDeleteOriginText =
              item.isDeleteOrigin === 0 ? "未删除" : "已删除";
          });
        }
        this.tableData = data.items;
        this.pagination.total = data.total;
      });
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1; // 搜索时重置到第一页
      this.fetchData(row); // 重新加载数据
    },

    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.fetchData(this.currentSearchParams);
    },

    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData(this.currentSearchParams);
    },

    // 详情和编辑
    handleOperation(row, type) {
      this.isEdit = true;
      this.publicOpinionData = row;
      this.publicOpinionType = type;
    },

    handleClose() {
      this.isEdit = false;
    },
  },
};
</script>

<style lang="scss" scoped></style>
