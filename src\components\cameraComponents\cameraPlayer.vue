<template>
  <div class="videoHkdom">
    <div class="iconfont icon2" @click="openVideoBox">&#xeb99;</div>
    <el-image
      v-if="loading"
      class="loading"
      :src="loadingImage"
      fit="contain"
    ></el-image>
    <div ref="videoDom" class="videoHK" :id="'hk' + id"></div>

    <general-dialog
      class="modelComVideo"
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      :showFooter="false"
      @cancel="onClose"
    >
      <div class="videoHkModedom">
        <el-image
          v-if="modelData.loading"
          class="loading"
          :src="loadingImage"
          fit="contain"
        ></el-image>
        <!-- <img v-if="modelData.loading" class="loading" :src="loadingImage" /> -->
        <div class="videoHK" :id="'modelhk' + id"></div>
      </div> 
    </general-dialog>

    <!-- <teleport to="#home" v-if="modelData.show">
      <transition name="fade">
        <modelBox
          :id="'modelComVideo'+ id"
          class="modelComVideo"
          title="视频监控"
          :close-btn="true"
          :isDrag="true"
          @onClose="onClose"
        >
          <div class="videoHkModedom">
            <el-image
              v-if="modelData.loading"
              class="loading"
              :src="loadingImage"
              fit="contain"
            ></el-image>
            <div class="videoHK" :id="'modelhk' + id"></div>
          </div>
        </modelBox>
      </transition>
    </teleport> -->
  </div>
</template>
<script>
import teleport from "@/components/teleport.vue";
import modelBox from "@/components/modelBox/index.vue";
import GeneralDialog from "../GeneralDialog.vue";
import { mapState } from "vuex";
export default {
  props: {
    url: {
      type: String,
      default: "",
    },
    id: {
      type: String,
      default: "",
    },
    modelData: {
      type: Object,
      default: {},
    },
  },

  data() {
    return {
      player: "",
      mode: 0,
      loadingImage: require("/assets/video/loding.gif"),
      loading: true,
      index: 0,

      // 添加错误监听器引用
      errorListener: null,
      // 添加错误捕获状态
      // hasRenderError: false
    };
  },
  components: {
    GeneralDialog,
    modelBox,
    teleport,
  },
  computed: {
    // ...mapState([
    //   "closeCemeteryPopup",
    //   "startInterval",
    //   "labourStartInterval",
    //   "labourStopInterval",
    // ]),
  },
  mounted() {
    this.init();

    // 设置全局错误监听
    this.setupGlobalErrorListener();
  },
  methods: {
    init() {
      setTimeout(() => {
        this.hkVideo("hk", this);
      }, 0);
    },

    hkVideo(type = "hk", data) {
      //初始化视频
      this.player = new window.JSPlugin({
        szId: type + this.id,
        szBasePath: "/hk/", //引入静态资源地址，我这里静态资源在public/js文件存放，所以设置为js
        iMaxSplit: 1,
        iCurrentSplit: 1,
        openDebug: true,
        oStyle: {
          borderSelect: "#FFCC00",
        },
      });
      this.realplay(this.url, 1, data);
    },

    realplay(playURL, index1, data) {
      this.mode = 0; //解码方式：0普通模式 1高级模式
      const { player, mode, urls } = this,
        index = player.currentWindowIndex;
      data.loading = true;
      player.JS_Play(playURL, { playURL, mode }, index1).then(
        () => {
          data.loading = false;
        },
        (e) => {
          console.error("xc111------失败了" + this.id);
          // 2秒之后回调错误信息，防止多个摄像头秒回错误信息导致的闪动的问题
          setTimeout(() => {
            this.$emit("playerError-block", this.id);
          }, 2000);
        }
      );
    },

    openVideoBox() {
      const currentVideoData = {id:this.id, url:this.url}
      this.$emit("openVideoBox", [currentVideoData]);
      // this.labourStopInterval();
      this.$store.commit("setVideoScreenStatus", 1);
    },

    onClose() {
      this.modelData.show = false;
      this.modelData.loading = false;
      // this.labourStartInterval()
      this.$store.commit("setVideoScreenStatus", 0);
    },

    setupGlobalErrorListener() {
      // 保存引用以便后续移除
      this.errorListener = (errorEvent) => {
        const error = errorEvent.error || errorEvent;
        
        // 检查是否为特定渲染错误
        if (error.stack && error.stack.includes('SR_DisplayFrameData') && 
            error.message.includes('clearColor')) {
          console.warn(`[${this.id}] 检测到渲染错误:`, error);
          
          // 标记错误状态
          // this.hasRenderError = true;
          
          // 触发自定义错误回调
          this.$emit("playerError-block", {
            id: this.id,
            errorType: "render_error",
            errorDetails: error
          });
          
          // 尝试安全停止播放器
          // this.safeStopPlayer();
          
          // 防止错误继续传播
          errorEvent.preventDefault();
          return true;
        }
      };
      
      // 添加错误监听
      window.addEventListener('error', this.errorListener, true);
    },
    
    // safeStopPlayer() {
    //   try {
    //     if (this.player && typeof this.player.JS_StopRealPlayAll === 'function') {
    //       console.error(`[${this.id}] 安全停止播放器时:`, e);
    //       this.player.JS_StopRealPlayAll();
    //     }
    //   } catch (e) {
    //     console.error(`[${this.id}] 停止播放器时出错:`, e);
    //   }
    // },

  },
  beforeDestroy() {
    //this.onClose()
    if (this.player) {
      this.player.JS_StopRealPlayAll();
    }
    // 清理时移除错误监听
    if (this.errorListener) {
      window.removeEventListener('error', this.errorListener, true);
    }
    
    //this.safeStopPlayer();
  },
};
</script>
<style lang="less" scoped>
.videoHkdom,
.videoHkModedom {
  width: 100%;
  height: 100%;
  position: relative;
  .videoHK {
    width: 100%;
    height: 100%;
  }
  .loading {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 9;
    background-color: #002244;
  }
  .iconfont {
    position: absolute;
    right: 12px;
    top: 12px;
    cursor: pointer;
    font-size: 32px;
    z-index: 10;
    font-weight: bold;
    background: #ffffff90;
    border-radius: 4px;
  }
}
.modelComVideo {
  position: absolute;
  right: 30px;
  top: 120px;
  width: 1170px;
  height: fit-content !important;
  background: rgba(0, 42, 81, 0.85);
  backdrop-filter: blur(4px);
  z-index: 10;
  .videoHkModedom {
    height: 878px;
    width: 100%;
  }
}
/* 进入和离开的激活状态 */
.fade-enter-active,
.fade-leave-active {
  transition: transform 0.5s;
}
/* 进入的起始状态和离开的结束状态 */
.fade-enter,
.fade-leave-to {
  transform: translateY(100%);
}
</style>
