<template>
  <general-dialog
    :dialog-visible="dialogVisible"
    :dialog-width="dialogWidth"
    :general-dialog-title="generalDialogTitle"
    :set-component-name="$store.getters.componentName"
    @cancel="handleCancel"
    @confirm="handleSubmit"
  >
    <div class="original-binding-div">
      <div class="original-data-list">
        <portal-table
          ref="originalTableRef"
          :add-permission="'user:add'"
          :columns="columns"
          :show-index="false"
          :show-add-button="false"
          :is-click-row-select="false"
          :pagination="pagination"
          :search-items="searchItems"
          :table-data="tableData"
          :table-height="500"
          :select-all-type="1"
          row-key="cameraId"
          @search="handleSearch"
          @handle-size-change="handleSizeChange"
          @handle-current-change="handleCurrentChange"
          @handle-checkbox-select="handleCheckboxSelect"
        />
      </div>
      <div class="binding-data-list">
        <portal-table
          ref="bindingTableRef"
          :add-permission="'user:add'"
          :columns="columns"
          :show-index="false"
          :show-watch="false"
          :table-height="500"
          :show-add-button="false"
          :is-click-row-select="false"
          :show-pagination="false"
          :table-data="tableBindingData"
          row-key="cameraId"
          @search="handleBindingSearch"
          @handle-checkbox-select="handleBindingCheckboxSelect"
        />
      </div>
    </div>
  </general-dialog>
</template>
<script>
import GeneralDialog from "../GeneralDialog.vue";
import PortalTable from "../PortalTable/index.vue";
// import $api from "@/api";
import { systemManagementApi, systemConfigApi } from "@/api";

export default {
  name: "BindingCamera",
  components: { PortalTable, GeneralDialog },
  data() {
    return {
      dialogVisible: false,
      dialogWidth: "1600px",
      generalDialogTitle: "摄像头绑定",

      columns: [
        { text: true, prop: "cameraId", label: "编号", align: "center" },
        { text: true, prop: "cameraName", label: "摄像头名称", align: "center" },
        {
          action: true,
          label: "操作",
          width: "90",
          operationList: [
            {
              label: "播放",
              permission: "user:edit",
              buttonClick: "",
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      searchItems: [
        {
          label: "",
          prop: "cameraName",
          type: "input",
          placeholder: "请输入摄像头名称",
        },
        {
          label: "",
          prop: "cameraId",
          type: "input",
          placeholder: "请输入摄像头编号",
        },
      ],
      currentSearch: null,
      searchParams: null,
      // 源数据
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },

      // 绑定数据
      tableBindingData: [],
      paginationBinding: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      },
      // 记录右边表格上一次的选中状态
      lastBindingSelection: [],
      // 记录所有选中数据的 id
      allSelectedIds: [],
    };
  },
  mounted() {},
  methods: {
    init(row) {
      this.dialogVisible = true;
      this.tableData = [
        // {
        //   cameraId: 1,
        //   cameraName: "摄像头1",
        // },
        // {
        //   cameraId: 2,
        //   cameraName: "摄像头2",
        // },
        // {
        //   cameraId: 3,
        //   cameraName: "摄像头3",
        // },
        // {
        //   cameraId: 4,
        //   cameraName: "摄像头4",
        // },
      ]; 
      if (!row.cgcs2000Latitude || !row.cgcs2000Longitude) {
        this.$message.error("该事件没有经纬度信息，无法获取摄像头");
        return;
      }     
      this.searchParams = {
        latitude: row.cgcs2000Latitude,
        longitude: row.cgcs2000Longitude,
        radius: 1,
        spotId: row.id,
        spotName: row.spotName
      }
      this.fetchData(true);
      // this.getBindCamera();
    },

    handleSubmit() {
      const params = {
        spotId: this.searchParams.spotId,
        spotName: this.searchParams.spotName,
        cameraIdS: this.allSelectedIds,
      };
      $api.spotCameraApi.bindSpotCamera(params).then((res) => {
        const { code, data, message, error } = res.data;
        if (code !== 0) return this.$message.error(message || error);
        this.$message.success('绑定成功')
        this.handleCancel();
      });
    },

    handleCancel() {
      this.dialogVisible = false;

      this.tableData = [];
      this.searchParams = null;
      this.currentSearch = null;

      this.tableBindingData = [];
      this.lastBindingSelection = [];
      this.allSelectedIds = [];
      this.pagination = {
        currentPage: 1,
        pageSize: 10,
        total: 0,
      };
    },

    /**
     * 源数据
     */
    fetchData(isQueryBind = false) {
      let params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...this.searchParams,
        ...this.currentSearch,
      };
      $api.spotCameraApi.querySpotCameraPage(params).then((res) => {
        const { code, data, message, error } = res.data;
        if (code !== 0) return this.$message.error(message || error);
        this.tableData = data.items;
        this.pagination.total = data.total;
        // 第一次进来获取已绑定的摄像头回显
        if (isQueryBind) {
          this.getBindCamera();
        } else {
          // 翻页时回显选中状态
          this.$nextTick(() => {
            this.setOriginalTableSelection();
          });
        }
      });
    },

    // 获取已经绑定的摄像头
    getBindCamera() {
      $api.spotCameraApi.querySpotCameraList({spotId : this.searchParams.spotId}).then((res) => {
        const { code, data, message, error } = res.data;
        if (code !== 0) return this.$message.error(message || error);
        this.tableBindingData = data;
        this.allSelectedIds = this.tableBindingData.map((item) => item.cameraId);
        this.$nextTick(() => {
          this.fetchBindingData();
          this.setOriginalTableSelection();          
        });
      });
    },

    // 条件查询
    handleSearch(row) {
      this.pagination.currentPage = 1;
      this.currentSearch = row;
      this.fetchData();
    },
    // 页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.fetchData();
    },
    // 条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.fetchData();
    },

    //源数据全选和单个选中的操作逻辑
    handleCheckboxSelect(selection, row, isAll) {
      if (isAll) {
        if (selection.length > 0) {
          selection.forEach((item) => {
            if (
              !this.tableBindingData.some(
                (bindingItem) => bindingItem.cameraId === item.cameraId
              )
            ) {
              this.tableBindingData.push(item);
              this.allSelectedIds.push(item.cameraId);
            }
          });
        } else {
          const currentPageIds = row.map((item) => item.cameraId);
          // 删除 tableBindingData 中匹配的 cameraId，不能用filter，filter不会改变原数组，需要重新赋值，重新赋值会导致数据刷新出现勾选状态没有勾选的问题
          for (let i = this.tableBindingData.length - 1; i >= 0; i--) {
            if (currentPageIds.includes(this.tableBindingData[i].cameraId)) {
              this.tableBindingData.splice(i, 1); // 直接删除
            }
          }
          // 过滤掉当前页面的 id
          this.allSelectedIds = this.allSelectedIds.filter(
            (cameraId) => !currentPageIds.includes(cameraId)
          );
        }
      } else {        
        if (row && row.isSelected) {
          this.tableBindingData.push(row);
          this.allSelectedIds.push(row.cameraId);
        } else {
          // 先找出元素的index，再将其从原数组里面删除，直接改变原数组，如果用filter不会改变原数组
          const itemIndex = this.tableBindingData.findIndex(item => item.cameraId === row.cameraId);
          if (itemIndex !== -1) {
            this.tableBindingData.splice(itemIndex, 1);
          }
          this.allSelectedIds = this.allSelectedIds.filter(
            (cameraId) => row.cameraId !== cameraId
          );
        }
      }
      this.fetchBindingData();
    },

    /**
     * 绑定数据
     * @param searchParams
     */
    fetchBindingData(searchParams) {
      let params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...searchParams,
      };
      this.paginationBinding.total = this.tableBindingData.length;
      this.$refs.bindingTableRef.splite(this.tableBindingData, true);
    },
    // 条件查询
    handleBindingSearch(row) {
      this.paginationBinding.currentPage = 1;
      this.fetchBindingData(row);
    },

    //绑定全选和单个选中的操作逻辑
    handleBindingCheckboxSelect(selection, row, isAll) {
      if (isAll) {
        this.tableBindingData = [];
        this.allSelectedIds = [];
        this.changeOriginalSelected(null);
      } else {
        this.tableBindingData.forEach((item, index) => {
          if (item.cameraId === row.cameraId) {
            this.tableBindingData.splice(index, 1);
          }
        });
        this.allSelectedIds = this.allSelectedIds.filter((cameraId) => cameraId !== row.cameraId);
        this.changeOriginalSelected(row);
        //回显选中状态
        this.fetchBindingData();
      }
    },

    //改变源数据的选中状态
    changeOriginalSelected(data) {
      this.tableData.forEach((row) => {
        if (!data || data.cameraId === row.cameraId) {
          row.isSelected = false;
          this.$refs.originalTableRef.$refs.tableRef.toggleRowSelection(
            row,
            false
          );
        }
      });
    },

    //源数据分页后回显选中的数据
    setOriginalTableSelection() {
      this.allSelectedIds.forEach((item) => {
        this.tableData.forEach((row) => {
          if (item === row.cameraId) {
            row.isSelected = true;
            this.$refs.originalTableRef.$refs.tableRef.toggleRowSelection(
              row,
              true
            );
          }
        });
      });
    },
  },
};
</script>
<style scoped lang="scss">
.original-binding-div {
  display: flex;
  padding: 30px 60px 5px 60px;

  .binding-data-list {
    margin-top: 70px;
    margin-left: 10px;
    width: 50%;
  }
}
</style>
