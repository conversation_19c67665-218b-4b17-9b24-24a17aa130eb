<!-- 避难场所 EmergencyShelter -->
<template>
  <div class="user-index-container">
    <portal-table
      style="padding: 20px"
      :showAddButton="false"
      :showSelection="false"
      :columns="columns"
      :pagination="pagination"
      :search-items="searchItems"
      :table-data="tableData"
      row-key="name"
      @search="handleSearch"
      @handle-size-change="handleSizeChange"
      @handle-current-change="handleCurrentChange"
    />

    <general-dialog
      :dialog-visible="dialogVisible"
      :dialog-width="dialogWidth"
      :general-dialog-title="generalDialogTitle"
      :set-component-name="$store.getters.componentName"
      @cancel="handleCancel"
      @confirm="handleSubmit"
    >
      <el-form
        ref="addForm"
        :model="form"
        :rules="rules"
        class="addCustForm"
        inline
        label-position="top"
        label-width="150px"
      >
      
        <el-divider content-position="left">基本信息</el-divider>

        <el-form-item label="场所名称" prop="refugeName">
          <el-input :disabled="styleType === 3" style="width: 184px;" v-model="form.refugeName" placeholder="请输入" />
        </el-form-item>

        <el-form-item label="行政区划" prop="county">
          <el-select :disabled="styleType === 3" style="width: 184px;" v-model="form.county" placeholder="请选择" @change="handleChangeArea">
            <el-option
            v-for="item in countyList"
            :key="item.code"
            :label="item.townName"
            :value="item.code"
            >
            </el-option>
          </el-select>
          <el-select :disabled="styleType === 3" style="margin-left: 10px; width: 184px;" v-model="form.street" placeholder="请选择">
            <el-option
            v-for="item in streetList"
            :key="item.code"
            :label="item.townName"
            :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="经度" prop="teamLongitude">
          <el-input :disabled="styleType === 3" type="text" v-model="form.teamLongitude" autocomplete="off" placeholder="请输入"
            style="width: 160px"></el-input>  例如:116.4074
        </el-form-item>
        <el-form-item label="纬度" prop="teamLatitude" >
          <el-input :disabled="styleType === 3" type="text" v-model="form.teamLatitude" autocomplete="off" placeholder="请输入"
            style="width: 160px"></el-input>  例如:39.9042
        </el-form-item>
        <el-form-item label="详细地址" prop="detailedAddress">
          <el-input :disabled="styleType === 3" style="width: 400px;" v-model="form.detailedAddress" placeholder="请输入" />
        </el-form-item>

        <el-divider content-position="left">场所规划设计</el-divider>

        <el-form-item label="场所分级" prop="placeGrading">
          <el-select :disabled="styleType === 3" v-model="form.placeGrading" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in placeGradingList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="依托资源类型" prop="resourceType">
          <el-select :disabled="styleType === 3" v-model="form.resourceType" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in resourceTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否特定避难场所" prop="isSpecific">
          <el-select :disabled="styleType === 3" v-model="form.isSpecific" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in trueOrFalseTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="空间类型" prop="spaceType">
          <el-select :disabled="styleType === 3" v-model="form.spaceType" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in spaceTypeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="总体功能" prop="allFunction">
          <el-select :disabled="styleType === 3" v-model="form.allFunction" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in allFunctionList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="避难时长" prop="refugeTime">
          <el-select :disabled="styleType === 3" v-model="form.refugeTime" placeholder="请选择" style="width: 184px;">
            <el-option
              v-for="item in refugeTimeList"
              :key="item.id"
              :label="item.itemName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </general-dialog>

    <!-- <el-dialog title="预览" :visible.sync="showIframe" v-if="showIframe" width="80%" height='600px' :before-close="handleCloseIframe">
      <iframe :src="preFileUrl" frameborder="0" width="100%" height="600px"></iframe>
    </el-dialog> -->
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import GeneralDialog from "@/components/GeneralDialog.vue";
import { emergencyKnowledgeBaseApi } from "@/api";
import 
{ 
  getItemList, 
  getTownsBeijingList,
  placeGradingType,
  resourceType,
  spaceType,
  allFunction,
  refugeTime
 } from "@/utils/dictionary";

import { getCurrentDate, getIPAddress } from "@/utils/publicMethod";
import { auth } from "@/utils"; 

export default {
  name: "EmergencyShelter",
  components: {
    GeneralDialog,
    PortalTable,
  },
  data() {
    return {
      searchItems: [
        {
          prop: "refugeName",
          label: "场所名称",
          type: "input",
          placeholder: "请输入",
          width: "150",
        },
        {
          prop: "placeGrading",
          label: "场所分级",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
        {
          prop: "resourceType",
          label: "资源类型",
          type: "select",
          placeholder: "请选择",
          width: "120",
          options: [],
        },
      ],
      columns: [
        { prop: "refugeName", label: "场所名称", text: true },
        { prop: "equipmentType", label: "行政区划", text: true },
        { prop: "placeGrading", label: "场所分级", text: true },
        { prop: "resourceType", label: "资源类型", text: true },
        {
          action: true, //是否显示操作
          label: '操作',
          width: '230px',
          operationList: [
            {
              label: '查看',
              permission: 'shelter:view',
              buttonClick: this.handleReview,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '编辑',
              permission: 'shelter:edit',
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true
              }
            },
            {
              label: '删除',
              permission: 'shelter:delete',
              buttonClick: this.handleDelete,
              isShow: (row, $index) => {
                return true
              }
            },
          ]
        }
      ],
      tableData: [],

      styleType: 1, //1：新增，2：编辑，3：查看
      trueOrFalseTypeList:[
        {itemName: "是", id: 1},
        {itemName: "否", id: 0}
      ],
      placeGradingList: [],
      resourceTypeList:[],
      spaceTypeList:[],
      allFunctionList: [],
      refugeTimeList: [],
      countyList:[
        // {townName:'朝阳区',code:'1'},
        // {townName:'海淀区',code:'2'},
        // {townName:'房山区',code:'3'},
        // {townName:'密云区',code:'4'},
        // {townName:'通州区',code:'5'}
      ],
      streetList:[
        // {townName:'长安街道',code:'1'},
        // {townName:'龙阳大道',code:'2'},
        // {townName:'明珠大道',code:'3'},
        // {townName:'民治街道',code:'4'},
        // {townName:'龙华街道',code:'5'}
      ],

      fileBaseUrl: "",
      fileList: [],
      showIframe: false,
      preFileUrl: "",

      searchParams: null,
      // 页码
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      // 弹框
      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增避难场所",

      form: {
        refugeName: "",
        county: "",
        street: "",
        teamLongitude: "",
        teamLatitude: "",
        detailedAddress: "",
        placeGrading: 0,
        resourceType: "",
        isSpecific: "",
        spaceType: "",
        allFunction: "",
        refugeTime: "",
        // fileList:[]
      },
      rules: {
        refugeName: [
          {required: true, message: '场所名称不能为空', trigger: 'blur'}
        ],
        county: [
          {required: true, message: '行政区划不能为空', trigger: 'blur'}
        ],
        teamLongitude: [
          {required: true, message: '经度不能为空', trigger: 'blur'}
        ],
        teamLatitude: [
          {required: true, message: '纬度不能为空', trigger: 'blur'}
        ],
        detailedAddress: [
          {required: true, message: '详细地址不能为空', trigger: 'blur'}
        ],
        placeGrading: [
          {required: true, message: '场所分级不能为空', trigger: 'blur'}
        ],
        resourceType: [
          {required: true, message: '依托资源类型不能为空', trigger: 'blur'}
        ],
        isSpecific: [
          {required: true, message: '是否特定避难场所不能为空', trigger: 'blur'}
        ],
        spaceType: [
          {required: true, message: '空间类型不能为空', trigger: 'blur'}
        ],
        allFunction: [
          {required: true, message: '总体功能不能为空', trigger: 'blur'}
        ],
        refugeTime: [
          {required: true, message: '避难时长不能为空', trigger: 'blur'}
        ],
      },
    };
  },
  mounted() {
    // this.fileBaseUrl = auth.getFileBaseUrl();
    this.getTableDataList();
    this.queryDictionaryType();
    this.registerHandlers();
  },
  methods: {
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.handleAdd,
      });
    },
    handleChangeMonth(value) {
      this.form.purchaseTime = getCurrentDate("yyyy",value);
    },
    //查询字典类型
    async queryDictionaryType() {
      try {
        this.placeGradingList = await getItemList(placeGradingType);
        this.searchItems[1].options = this.placeGradingList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.resourceTypeList = await getItemList(resourceType);
        this.searchItems[2].options = this.resourceTypeList.map((item) => ({
          label: item.itemName,
          value: item.id
        }))
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.spaceTypeList = await getItemList(spaceType);
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.allFunctionList = await getItemList(allFunction);
      } catch (error) {
        this.$message.error(error.message);
      }

      try {
        this.refugeTimeList = await getItemList(refugeTime);
      } catch (error) {
        this.$message.error(error.message);
      }

      // 行政区划列表
      try {
        this.countyList = await getTownsBeijingList({ parent: 0 });
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    // 联动获取街道列表
    async handleChangeArea(value) {
      console.log('xc----value',value);
      this.form = {
        ...this.form,
        street: ""
      };
      try {
        this.streetList = await getTownsBeijingList({ parent: value });
      } catch (error) {
        this.$message.error(error.message);
      }
    },

    //查看详情
    getRowDataInfo(row) {
      // this.fileList = [];
      // this.form.fileList = [];
      emergencyKnowledgeBaseApi.queryRefugeInfo({ id: row.id }).then((res) => {
        const { code, data, message, error } = res;
        if (code !== 0) return this.$message.error(message || error);
        // if (data.fileList && data.fileList.length > 0) {
        //   data.fileList.forEach((row) => {
        //     this.fileList.push({
        //       name: row.fileName,
        //       url: this.fileBaseUrl + row.fileUrl,
        //       id: row.id,
        //     });
        //   });
        // } else {
        //   data.fileList = [];
        // }
        this.form = {
          ...data,
        };
      });
    },

    //新增
    handleAdd() {
      this.styleType = 1;
      this.dialogVisible = true;
      this.form = {};
      // this.fileList = [];
      // this.form.fileList = [];
      this.generalDialogTitle = "新增避难场所";
    },

    //编辑
    handleEdit(row) {
      this.styleType = 2;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "编辑避难场所";
    },

    //查看
    handleReview(row) {
      this.styleType = 3;
      this.dialogVisible = true;
      this.getRowDataInfo(row);
      this.generalDialogTitle = "查看避难场所";
    },

    //删除
    handleDelete(row) {
      this.$confirm('此操作将永久删除该条数据, 是否继续?', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        emergencyKnowledgeBaseApi.deleteRefugeInfo({ id: row.id }).then((res) => {
          const { code, message, error } = res;
          if (code !== 0) return this.$message.error(message || error);
          this.$message.success('删除成功');
          this.handSubmitSuccess();
        })
      })
    },

    // // 通报
    // handleApproval() {
    //   setTimeout(() => {
    //     this.$message.success("报送成功！");
    //   }, 500);
    // },

    // 查询列表
    async getTableDataList() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.current,
        ...this.searchParams
      };
      const res = await emergencyKnowledgeBaseApi.queryRefugeInfoPage(params);
      const { code, data, message, error } = res;
      if (code !== 0) return this.$message.error(message || error)
      this.tableData = data.items || [];
      this.pagination.total = data.total;
    },

    // 搜索
    handleSearch(row) {
      this.pagination.currentPage = 1
      if (row.inspectionTime && row.inspectionTime.length > 0) {
        row.startTime = conversionDateNotSecond(row.inspectionTime[0])
        row.endTime = conversionDateNotSecond(row.inspectionTime[1])
        delete row.inspectionTime
      }
      this.searchParams = row;
      this.getTableDataList();
    },
    // 处理每页显示条数变化
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.currentPage = 1;
      this.getTableDataList();
    },
    // 处理当前页码变化
    handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.getTableDataList();
    },

    // 取消弹框
    handleCancel() {
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (this.styleType === 1) {
            const res = await emergencyKnowledgeBaseApi.createRefugeInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('新增成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          } else {
            const res = await emergencyKnowledgeBaseApi.updateRefugeInfo(this.form);
            const {code, error} = res;
            if (code === 0) {
              this.$message.success('修改成功')
              this.handSubmitSuccess();
            } else {
              this.$message.error(error)
            }
          }
        } else {
          return false;
        }
      });
    },

    handSubmitSuccess() {
      this.getTableDataList();
      this.dialogVisible = false;
      this.$refs.addForm.resetFields();
    },

    resetFromData() {
      this.form = {
        refugeName: "",
        county: "",
        street: "",
        teamLongitude: "",
        teamLatitude: "",
        detailedAddress: "",
        placeGrading: 0,
        resourceType: "",
        isSpecific: "",
        spaceType: "",
        allFunction: "",
        refugeTime: "",
        // fileList:[]
      };
    },

    // // 新增上传方法
    // uploadFile(file) {
    //   // 文件大小校验
    //   this.fileList = [];
    //   const MAX_SIZE = 100 * 1024 * 1024; // 100MB
    //   if (file.file.size > MAX_SIZE) {
    //     this.$message.error("文件大小超过100MB限制");
    //     this.$refs.uploadRef.clearFiles();
    //     return;
    //   }
    //   const formData = new FormData();
    //   formData.append("file", file.file);

    //   systemManagementApi.uploadFile(formData).then((res) => {
    //     this.$refs.uploadRef.clearFiles();
    //     const { code, data, message, error } = res;
    //     if (code !== 0) return this.$message.error(message || error);
    //     this.$message.success("上传成功");
    //     setTimeout(() => {
    //       const fileUrl = this.fileBaseUrl + data.fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileUrl = fileUrl;
    //       // data.fileType = getFileExtension(data.url);

    //       this.form.fileList.push(data);
    //       this.fileList.push({
    //         name: data.fileName,
    //         url: fileUrl,
    //         id: data.id,
    //       });
    //     }, 500);
    //   });
    // },
    // handleRemove(file, fileList) {
    //   this.form.fileList = this.form.fileList.filter((item) => item.id !== file.id);
    // },
    // handlePreview(file) {
    //   let fileUrl = file.url;
    //   const fileExtension = fileUrl.split(".").pop().toLowerCase();
    //   const previewableExtensions = ["pdf", "jpg", "jpeg", "png", "gif"];
    //   if (!previewableExtensions.includes(fileExtension)) {
    //     // 如果文件类型不支持直接预览，则重新拼接URL
    //     fileUrl ='http://' + getIPAddress() + ':8012/onlinePreview?url=' + encodeURIComponent(btoa(file.url));
    //   } 
    //   window.open(fileUrl, '_blank');
    // },
    // handleExceed(files, fileList) {
    //   this.$message.warning("只能上传一个文件");
    //   // this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    // },
    // beforeRemove(file, fileList) {
    //   return this.$confirm(`确定移除 ${file.name}？`);
    // },

    // // 关闭预览框
    // handleCloseIframe() {
    //   this.showIframe = false;
    // },
  },

};
</script>

<style lang="scss" scoped>
.addCustForm {
  padding: 30px 60px 5px 60px;

  .el-form-item {
    margin-right: 30px !important;  
    margin-bottom: 10px !important;
  }

  .el-form-item__label {
    font-weight: 700 !important;
    font-size: 14px;
    color: #323233;
  }

  .el-form-item--small.el-form-item {
    margin-bottom: 15px;
  }

  .el-select {
    width: 100%;
  }
}
</style>
