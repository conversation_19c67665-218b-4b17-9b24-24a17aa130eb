{"name": "comprehensive-info-system", "version": "1.0.0", "description": "应急值守业务系统", "main": "index.js", "scripts": {"npm:install": "npm install && npm run dev", "dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"moment": "^2.30.1", "echarts": "^5.6.0", "axios": "^1.10.0", "crypto-js": "^4.2.0", "element-ui": "^2.15.13", "vue": "^2.6.14", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "sass": "^1.32.0", "sass-loader": "^10.2.0", "vue-template-compiler": "^2.6.14"}}