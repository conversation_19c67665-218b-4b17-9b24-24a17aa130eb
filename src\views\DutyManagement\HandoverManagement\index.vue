<!-- 交接班管理 - Page -->
<template>
  <div style="padding: 20px">
     <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="值班值守人员" name="0">
          <dutyShiftTab></dutyShiftTab>
        </el-tab-pane>

        <el-tab-pane 
          v-for="tab in generalTabs" 
          :key="tab.name"
          :label="tab.label" 
          :name="tab.name"
        >
          <dutyShiftGeneralTab :ref="`generalTab_${tab.name}`" v-show="activeName === tab.name" :tabType="tab.name"></dutyShiftGeneralTab>
        </el-tab-pane>

      </el-tabs>
  </div>
</template>

<script>

import dutyShiftTab from './components/dutyShiftTab.vue';
import dutyShiftGeneralTab from './components/dutyShiftGeneralTab.vue';

export default {
  name: "HandoverManagement",
  components: {
    dutyShiftTab,
    dutyShiftGeneralTab,
  },
  data() {
    return {
      activeName: '0',
      generalTabs: [
        { name: '1', label: '预警发布及天气情况' },
        { name: '2', label: '风险分析' },
        { name: '3', label: '指挥调度及相关会议' },
        { name: '4', label: '批示接收与办理' },
        { name: '5', label: '突发情况接报与处置' },
        { name: '6', label: '防汛工作' },
        { name: '7', label: '高危监测' },
        { name: '8', label: '信息化保障' },
        { name: '9', label: '视频调取和实战演练' },
        { name: '10', label: '注意事项' },
      ]
    };
  },
  methods: {
    handleClick(tab, event) {
      // console.log(this.$refs[`generalTab_${tab.name}`]);
      // 通过ref调用组件方法
      this.$nextTick(() => {
        if (this.$refs[`generalTab_${tab.name}`] && this.$refs[`generalTab_${tab.name}`][0]) {
          this.$refs[`generalTab_${tab.name}`][0].loadData(tab.name);
        }
      });
    }
  }
};
</script>


<style lang="scss" scoped>

</style>