<template>
  <div class="to-examine-container">
    <el-card>
      <el-form :inline="true" :model="searchData" class="demo-form-inline">
        <el-form-item label="单位名称/值班员">
          <el-input
            v-model="searchData.name"
            placeholder="单位名称/值班员"
          ></el-input>
        </el-form-item>
        <el-form-item label="评价状态">
          <el-select v-model="searchData.status" placeholder="请选择">
            <el-option
              v-for="item in statusList"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="onSubmit"
            >高级筛选</el-button
          >
          <el-button type="primary" icon="el-icon-refresh" @click="onReset"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <PortalTable
        :tableHeight="500"
        :columns="columns"
        :table-data="tableData"
        row-key="id"
        :pagination="pagination"
        :showAddButton="false"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />
    </el-card>
    <PublicDialog ref="publicDialogRef" @refreList="queryComprehensiveList" />
  </div>
</template>

<script>
import PortalTable from "@/components/PortalTable/index.vue";
import comprehensiveAssessmentApi from "@/api/comprehensiveAssessment";
import PublicDialog from "@/views/ComprehensiveAssessment/UserEvaluationReporting/components/publicDialog.vue";

export default {
  name: "ToExamine",
  components: {
    PortalTable,
    PublicDialog,
  },
  data() {
    return {
      // 0:草稿 1:待审核 2:审核驳回 3:审核通过
      statusList: [
        {
          label: "草稿",
          value: 0,
        },
        {
          label: "待审核",
          value: 1,
        },
        {
          label: "审核驳回",
          value: 2,
        },
        {
          label: "审核通过",
          value: 3,
        },
        {
          label: "待录入",
          value: 4,
        },
      ],
      searchData: {
        name: "",
        status: "",
      },

      columns: [
        { text: true, prop: "orgName", label: "单位名称", width: 180 },
        { text: true, prop: "userName", label: "值班员", width: 180 },
        {
          text: true,
          prop: "totalSelfRatedScore",
          label: "自评分数",
          width: 100,
        },
        { text: true, prop: "totalSystemScore", label: "系统评分", width: 100 },
        { text: true, prop: "totalFinalScore", label: "综合得分", width: 100 },
        {
          text: true,
          prop: "statusFormat",
          label: "评价状态",
          width: 100,
        },
        {
          action: true,
          label: "操作",
          operationList: [
            {
              label: "查看详情",
              permission: "evaluationCriteria:detail",
              buttonClick: this.handleDetail,
              isShow: (row, $index) => {
                return row.status !== 4;
              },
            },
            {
              label: "审核",
              permission: "evaluationCriteria:audit",
              buttonClick: this.handleAudit,
              isShow: (row, $index) => {
                return row.status === 1;
              },
            },
            {
              label: "编辑",
              permission: "evaluationCriteria:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return row.status === 0 || row.status === 2;
              },
            },
            {
              label: "录入",
              permission: "evaluationCriteria:edit",
              buttonClick: this.handleEntry,
              isShow: (row, $index) => {
                return row.status === 4;
              },
            },
            // {
            //   label: "删除",
            //   permission: "evaluationCriteria:delete",
            //   buttonClick: this.handleDelete,
            //   isShow: (row, $index) => {
            //     return true;
            //   },
            // },
          ],
        },
      ],
      tableData: [],
      pagination: {
        total: 30,
        pageSize: 10,
        currentPage: 1,
      },
    };
  },
  methods: {
    // 查询用户评价上报分页列表
    queryComprehensiveList() {
      comprehensiveAssessmentApi
        .queryComprehensiveList({
          page: this.pagination.currentPage,
          count: this.pagination.pageSize,
          ...this.searchData,
        })
        .then((res) => {
          this.tableData = res.data.items;
          this.tableData.forEach((item) => {
            item.statusFormat = this.statusList.find(
              (status) => status.value === item.status
            )?.label;
          });
          this.pagination.total = res.data.total;
        });
    },
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.queryComprehensiveList();
    },
    handleCurrentChange(currentPage) {
      this.pagination.currentPage = currentPage;
      this.queryComprehensiveList();
    },
    onSubmit() {
      this.pagination.currentPage = 1;
      this.queryComprehensiveList();
    },
    onReset() {
      this.searchData = {
        name: "",
        status: "",
      };
      this.queryComprehensiveList();
    },
    handleEdit(row) {
      console.log("编辑", row);
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "edit";
      this.$refs.publicDialogRef.title = "编辑用户评价上报";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    handleEntry(row) {
      console.log("录入", row);
      this.queryComprehensiveRead(row);
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "entry";
      this.$refs.publicDialogRef.title = "用户评价上报录入";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    handleDelete(row) {
      console.log("删除", row);
      this.$confirm("确认删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          comprehensiveAssessmentApi
            .deleteComprehensive({
              id: row.id,
            })
            .then((res) => {
              this.$message({
                message: "删除成功",
                type: "success",
              });
              this.queryComprehensiveList();
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    handleDetail(row) {
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "detail";
      this.$refs.publicDialogRef.title = "用户评价上报详情";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    handleAudit(row) {
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "audit";
      this.$refs.publicDialogRef.title = "用户评价上报审核";
      this.$refs.publicDialogRef.form.id = row.id;
      this.$refs.publicDialogRef.auditForm = {
        id: "",
        auditFinalScore: 0,
        auditRemark: "",
        auditOpinion: "",
        status: 0,
      };
      this.$refs.publicDialogRef.queryComprehensiveById();
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addTopHandler,
      });
    },
    addTopHandler() {
      // this.$refs.publicDialogRef.resetForm();
      this.$refs.publicDialogRef.dialogVisible = true;
      this.$refs.publicDialogRef.dialogType = "add";
      this.$refs.publicDialogRef.title = "新增用户评价上报";
      this.$refs.publicDialogRef.form.id = "";
      this.$refs.publicDialogRef.queryCriteriaList();
    },
    formatter(row, column) {
      console.log(row, column, "llllllllll");

      return "row.address";
    },
    // 评价阅读
    queryComprehensiveRead(row) {
      comprehensiveAssessmentApi.queryComprehensiveRead({
        id: row.id,
      });
    },
  },
  mounted() {
    this.queryComprehensiveList();
    this.registerHandlers();
  },
};
</script>

<style lang="scss" scoped>
.to-examine-container {
  height: 100%;
  padding: 20px;
  .title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .subtitle {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
  }
  .content {
    width: 100%;
    .card-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
      }
      .search {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>