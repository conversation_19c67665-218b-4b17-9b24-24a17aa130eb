<template>
  <div class="role-management-container">
    <div class="role-management-main">
      <!-- 右侧内容区域 -->
      <portal-table
        style="width: 100%"
        :showAddButton="false"
        :columns="columns"
        :pagination="pagination"
        :search-items="searchItems"
        :table-data="tableData"
        row-key="name"
        @search="handleSearch"
        @handle-size-change="handleSizeChange"
        @handle-current-change="handleCurrentChange"
      />

      <general-dialog
        :dialog-visible="dialogVisible"
        :dialog-width="dialogWidth"
        :general-dialog-title="generalDialogTitle"
        :set-component-name="$store.getters.componentName"
        @cancel="handleCancel"
        @confirm="handleSubmit"
      >
        <el-form
          ref="addForm"
          :model="form"
          :rules="rules"
          class="add-form"
          label-position="top"
          label-width="100px"
        >
          <el-form-item label="角色名称" prop="roleName">
            <el-input v-model="form.roleName" placeholder="请输入角色名称" />
          </el-form-item>
          <el-form-item label="角色分级" prop="userClass">
            <el-select v-model="form.userClass" placeholder="请输入角色分级">
              <el-option
                v-for="item in userClassList"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色描述" prop="roleDesc">
            <el-input
              v-model="form.roleDesc"
              :rows="6"
              placeholder="请输入角色描述"
              type="textarea"
            />
          </el-form-item>
        </el-form>
      </general-dialog>
      <edite-dialog-vue
        ref="editeDialogRef"
        @update="handleUpdateBySubcomponent"
      />
      <menu-assignment-dialog-vue
        ref="menuAssignmentDialogRef"
        @update="handleUpdateBySubcomponent"
      />
      <data-assignment-dialog-vue
        ref="dataAssignmentDialogRef"
        @update="handleUpdateBySubcomponent"
      />
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from "vuex";
import GeneralDialog from "@/components/GeneralDialog.vue";
import PortalTable from "@/components/PortalTable/index.vue";
import EditeDialogVue from "./components/editeDialog.vue";
import menuAssignmentDialogVue from "./components/menuAssignmentDialog.vue";
import dataAssignmentDialogVue from "./components/dataAssignmentDialog.vue";
import { systemManagementApi } from "@/api";
import { getItemList, userClassDictionaryId } from "@/utils/dictionary";

export default {
  name: "RoleManagement",
  components: {
    PortalTable,
    GeneralDialog,
    EditeDialogVue,
    menuAssignmentDialogVue,
    dataAssignmentDialogVue,
  },
  data() {
    return {
      tableData: [],
      columns: [
        { text: true, prop: "roleName", label: "角色名称" },
        { text: true, prop: "userClass", label: "角色分级" },
        { text: true, prop: "roleDesc", label: "角色描述" },
        { text: true, prop: "createTime", label: "创建时间" },
        { text: true, prop: "endTime", label: "截止时间" },
        { text: true, prop: "status", label: "角色状态" },
        {
          action: true,
          label: "操作",
          width: 360,
          operationList: [
            {
              label: "编辑",
              permission: "roleManagement:edit",
              buttonClick: this.handleEdit,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "分配菜单",
              permission: "roleManagement:distributionMenu",
              buttonClick: this.handleDistributionMenu,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "分配数据",
              permission: "roleManagement:distributionData",
              buttonClick: this.handleDistributionData,
              isShow: (row, $index) => {
                return true;
              },
            },
            {
              label: "注销",
              permission: "roleManagement:cancellation",
              buttonClick: this.handleCancellation,
              isShow: (row, $index) => {
                return true;
              },
            },
          ],
        },
      ],
      searchItems: [
        {
          prop: "roleName",
          label: "角色名称",
          type: "input",
          placeholder: "请输入角色名称",
        },
        {
          prop: "status",
          label: "角色状态",
          type: "select",
          placeholder: "请输入角色状态",
          defaultValue: "全部",
          options: [
            { label: "全部", value: "" },
            { label: "正常", value: "1" },
            { label: "注销", value: "3" },
          ],
        },
        {
          prop: "userClass",
          label: "角色分级",
          type: "select",
          placeholder: "请输入角色分级",
          defaultValue: "全部",
          options: [],
        },
      ],
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },

      dialogVisible: false,
      dialogWidth: "560px",
      generalDialogTitle: "新增角色",
      form: {
        roleName: "",
        userClass: "",
        roleDesc: "",
      },
      rules: {
        roleName: [
          { required: true, message: "请输入角色名称", trigger: "blur" },
        ],
        userClass: [
          { required: true, message: "请输入角色分级", trigger: "blur" },
        ],
        roleDesc: [
          { max: 200, message: "备注长度不能超过200", trigger: "blur" },
        ],
      },
      // 角色分级
      userClassList: [],
      searchKeyword: {
        roleName: "",
        status: "",
        userClass: "",
      },
    };
  },
  computed: {
    ...mapState("userManagement", ["selectedOrg"]),

    // 响应式表格列配置
    responsiveTableColumns() {
      const screenWidth = window.innerWidth;
      if (screenWidth < 1200) {
        // 小屏幕时隐藏部分列
        return this.tableColumns.filter((col) =>
          ["name", "phone", "department", "remark"].includes(col.prop)
        );
      }
      return this.tableColumns;
    },
  },
  mounted() {
    this.registerHandlers();
    this.queryRolePage();
    this.queryItemListById();
    // 监听窗口大小变化
    window.addEventListener("resize", this.handleResize);
  },

  beforeDestroy() {
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    // 查询角色数据
    async queryRolePage() {
      const params = {
        count: this.pagination.pageSize,
        page: this.pagination.currentPage,
        ...this.searchKeyword,
      };
      try {
        const res = await systemManagementApi.queryRolePage(params);
        this.tableData = res.data.items;
        this.pagination.total = res.data.total;
      } catch (error) {
        console.error("查询角色数据失败:", error);
        this.$message.error("查询角色数据失败");
      }
    },

    addHandler() {
      this.dialogVisible = true;
    },
    registerHandlers() {
      this.$store.commit("generalEvent/registerEventHandler", {
        type: "add_top",
        handler: this.addHandler,
      });
    },

    // 搜索相关方法
    async handleSearch(formData) {
      console.log(formData);

      this.pagination.currentPage = 1;
      this.pagination.pageSize = 10;
      if (formData.status === "全部") {
        formData.status = "";
      }
      if (formData.userClass === "全部") {
        formData.userClass = "";
      }
      this.searchKeyword = formData;
      console.log(this.searchKeyword);
      this.queryRolePage();
    },

    // 分页相关方法
    async handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.queryRolePage();
    },
    async handleCurrentChange(page) {
      this.pagination.currentPage = page;
      this.queryRolePage();
    },

    // 处理窗口大小变化
    handleResize() {
      // 强制重新计算响应式列配置
      this.$forceUpdate();
    },

    // 编辑
    handleEdit(row) {
      this.$refs.editeDialogRef.editeDialogVisible = true;
      this.$refs.editeDialogRef.dialogVisible = true;
      this.$refs.editeDialogRef.userClassList = this.userClassList;
      const find = this.userClassList.find(
        (item) => item.label === row.userClass
      );
      const form = {
        id: row.id,
        roleName: row.roleName,
        userClass: find?.value,
        roleDesc: row.roleDesc,
        status: row.status === "正常" ? "1" : "3",
      };
      this.$refs.editeDialogRef.form = form;
    },
    // 分配菜单
    handleDistributionMenu(row) {
      this.$refs.menuAssignmentDialogRef.menuAssignmentDialogVisible = true;
      this.$refs.menuAssignmentDialogRef.dialogVisible = true;
      const form = {
        id: row.id,
        roleName: row.roleName,
        userClass: row.userClass,
      };
      this.$refs.menuAssignmentDialogRef.form = form;
      this.$refs.menuAssignmentDialogRef.queryRoleMenu();
    },
    // 分配数据
    handleDistributionData(row) {
      this.$refs.dataAssignmentDialogRef.dataAssignmentDialogVisible = true;
      this.$refs.dataAssignmentDialogRef.dialogVisible = true;
      const form = {
        id: row.id,
        roleName: row.roleName,
        userClass: row.userClass,
      };
      this.$refs.dataAssignmentDialogRef.form = form;
      this.$refs.dataAssignmentDialogRef.queryRoleOrg();
    },
    // 注销
    handleCancellation(row) {
      this.$confirm("确认注销吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        systemManagementApi
          .disableRole({
            id: row.id,
          })
          .then((res) => {
            this.$message.success("注销成功");
            this.queryRolePage();
          });
      });
    },

    // 删除
    handleAudit(row) {
      this.$message.info(`审核用户: ${row.name}`);
      // TODO: 删除当前组织机构
    },

    //提交
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          console.log(this.form);
          systemManagementApi.createRole(this.form);
          this.dialogVisible = false;
          this.$refs.addForm.resetFields();
          this.pagination.currentPage = 1;
          this.pagination.pageSize = 10;
          this.searchKeyword = {
            roleName: "",
            status: "",
            userClass: "",
          };
          this.queryRolePage();
        }
      });
    },

    //取消
    handleCancel() {
      this.dialogVisible = false;
      this.form = {
        focusName: "", //重点类型名称
        focusCode: "", //重点类型代码
        remark: "", //备注
      };
    },
    // 角色分级
    async queryItemListById() {
      try {
        const res = await getItemList(userClassDictionaryId);

        res?.forEach((element) => {
          const obj = {
            value: element.id,
            label: element.itemName,
          };
          this.userClassList[element.itemSort] = obj;
        });
        this.searchItems.map((item) => {
          if (item.prop === "userClass") {
            item.options = [...this.userClassList];
            item.options[0] = {
              value: "",
              label: "全部",
            };
          }
        });
        this.userClassList = this.userClassList.filter(Boolean);
        console.log(this.userClassList, "角色分级");
      } catch (error) {
        console.error("查询角色分级数据失败:", error);
        this.$message.error("查询角色分级数据失败");
      }
    },
    // 弹框子组件更新，刷新表格
    handleUpdateBySubcomponent() {
      this.queryRolePage();
    },
  },
};
</script>

<style scoped>
.role-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.role-management-main {
  flex: 1;
  display: flex;
  gap: 8px;
  padding: 8px;
  overflow: hidden;
}

.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 0;
}

.search-wrapper {
  margin-bottom: 8px;
}

.table-wrapper {
  flex: 1;
  overflow: hidden;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 操作按钮样式 */
.table-wrapper >>> .el-button--text.el-button--mini {
  margin-right: 2px !important;
  padding: 2px 6px !important;
  font-size: 12px !important;
  min-width: auto !important;
  white-space: nowrap !important;
  line-height: 1.4 !important;
}

/* 最后一个按钮不需要右边距 */
.table-wrapper >>> .el-button--text.el-button--mini:last-child {
  margin-right: 0 !important;
}

/* 查询按钮 - 使用Element UI primary颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(1) {
  color: #409eff !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(1):hover {
  color: #66b1ff !important;
}

/* 编辑按钮 - 使用Element UI success颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(2) {
  color: #67c23a !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(2):hover {
  color: #85ce61 !important;
}

/* 审核按钮 - 使用Element UI warning颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(3) {
  color: #e6a23c !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(3):hover {
  color: #ebb563 !important;
}

/* 分配角色按钮 - 使用Element UI info颜色 */
.table-wrapper >>> .el-button--text.el-button--mini:nth-child(4) {
  color: #909399 !important;
}

.table-wrapper >>> .el-button--text.el-button--mini:nth-child(4):hover {
  color: #a6a9ad !important;
}

/* 修改搜索公共组件样式 */
.search-wrapper ::v-deep .search-form .search-content {
  display: flex;
  align-items: center;
  gap: 16px;
}
.search-wrapper ::v-deep .search-form .search-content .search-row {
  margin-bottom: 0 !important;
}
.search-wrapper
  ::v-deep
  .search-form
  .search-content
  .search-row
  .search-item
  .el-input {
  min-width: 400px !important;
}
.search-wrapper ::v-deep .search-form .search-content .search-row {
  margin-bottom: 0 !important;
}
.search-wrapper ::v-deep .search-form .search-content .search-buttons-row {
  margin: 0px !important;
}
/* 修改搜索公共组件样式 */
</style>
